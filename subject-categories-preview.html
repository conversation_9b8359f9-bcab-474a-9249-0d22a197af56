<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Subject Categories - BioMed LMS</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    .gradient-bg {
      background: linear-gradient(135deg, #2563eb 0%, #7c3aed 50%, #059669 100%);
    }
    .card-hover {
      transition: all 0.3s ease;
    }
    .card-hover:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    .division-card {
      transition: all 0.3s ease;
      cursor: pointer;
    }
    .division-card:hover {
      transform: scale(1.05);
    }
    .subject-card {
      transition: all 0.2s ease;
    }
    .subject-card:hover {
      transform: translateY(-2px);
      border-color: #3b82f6;
    }
  </style>
</head>
<body class="bg-gray-50">
  <!-- Navigation -->
  <header class="bg-blue-700 text-white shadow-md">
    <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
      <div class="flex items-center space-x-4">
        <a href="navigation-index.html" class="text-2xl font-bold hover:text-blue-200 transition-colors">🏥 BioMed LMS</a>
        <span class="text-blue-300 text-sm">/ Subject Categories</span>
      </div>
      <div class="space-x-4">
        <a href="index.html" class="px-3 py-2 rounded-md text-sm font-medium bg-green-600 hover:bg-green-700 text-white">Main Landing</a>
        <a href="preview.html" class="px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-600 hover:text-white">Home</a>
        <a href="subject-categories-preview.html" class="px-3 py-2 rounded-md text-sm font-medium bg-blue-900 text-white">Subject Categories</a>
        <a href="enhanced-course-details-preview.html" class="px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-600 hover:text-white">Course Details</a>
        <a href="enhanced-homepage-preview.html" class="px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-600 hover:text-white">Enhanced Home</a>
        <a href="navigation-index.html" class="px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-600 hover:text-white">Navigation Hub</a>
      </div>
    </nav>
  </header>

  <!-- Header Section -->
  <section class="gradient-bg text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h1 class="text-4xl md:text-5xl font-bold mb-6">
        Biomedical Engineering Subject Categories
      </h1>
      <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
        Explore comprehensive courses across three core divisions of biomedical engineering
      </p>
      <div class="mt-8">
        <a href="#" class="inline-flex items-center text-blue-200 hover:text-white transition-colors">
          ← Back to Home
        </a>
      </div>
    </div>
  </section>

  <!-- Division Overview -->
  <section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">
          Choose Your Specialization
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Select a division to explore detailed courses and learning paths
        </p>
      </div>

      <div class="grid md:grid-cols-3 gap-8 mb-16">
        <!-- Division 1: Biomedical Imaging -->
        <div class="division-card relative overflow-hidden rounded-2xl shadow-lg cursor-pointer" onclick="window.location.href='biomedical-imaging-courses.html'">
          <div class="bg-gradient-to-br from-blue-500 to-blue-700 p-8 text-white">
            <div class="text-4xl mb-4">🔬</div>
            <h3 class="text-xl font-bold mb-3">Biomedical Imaging Instrumentation Technology</h3>
            <p class="text-blue-100 leading-relaxed">Advanced imaging technologies and instrumentation used in medical diagnosis and research.</p>
            <div class="mt-6 flex items-center justify-between">
              <span class="inline-flex items-center text-sm font-medium">
                6 Courses Available
                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </span>
              <span class="text-xs bg-white/20 px-2 py-1 rounded-full">18-20 ساعة معتمدة</span>
            </div>
          </div>
        </div>

        <!-- Division 2: Electrophysiological -->
        <div class="division-card relative overflow-hidden rounded-2xl shadow-lg cursor-pointer" onclick="window.location.href='electrophysiological-courses.html'">
          <div class="bg-gradient-to-br from-green-500 to-green-700 p-8 text-white">
            <div class="text-4xl mb-4">⚡</div>
            <h3 class="text-xl font-bold mb-3">Electrophysiological Instrumentation and Measurements</h3>
            <p class="text-green-100 leading-relaxed">Electrical activity measurement and analysis in biological systems.</p>
            <div class="mt-6 flex items-center justify-between">
              <span class="inline-flex items-center text-sm font-medium">
                6 Courses Available
                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </span>
              <span class="text-xs bg-white/20 px-2 py-1 rounded-full">20-22 ساعة معتمدة</span>
            </div>
          </div>
        </div>

        <!-- Division 3: Biomechanics -->
        <div class="division-card relative overflow-hidden rounded-2xl shadow-lg cursor-pointer" onclick="window.location.href='biomechanics-rehabilitation-courses.html'">
          <div class="bg-gradient-to-br from-purple-500 to-purple-700 p-8 text-white">
            <div class="text-4xl mb-4">🦴</div>
            <h3 class="text-xl font-bold mb-3">Biomechanics and Rehabilitation Engineering</h3>
            <p class="text-purple-100 leading-relaxed">Mechanical principles applied to biological systems and rehabilitation technologies.</p>
            <div class="mt-6 flex items-center justify-between">
              <span class="inline-flex items-center text-sm font-medium">
                6 Courses Available
                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </span>
              <span class="text-xs bg-white/20 px-2 py-1 rounded-full">19-21 ساعة معتمدة</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Sample Division Details (Biomedical Imaging) -->
      <div id="division-details" class="bg-white rounded-2xl shadow-xl p-8 mb-8">
        <div class="flex items-center mb-8">
          <div class="text-3xl mr-4">🔬</div>
          <div>
            <h3 class="text-2xl font-bold text-gray-900">Biomedical Imaging Instrumentation Technology</h3>
            <p class="text-gray-600">Advanced imaging technologies and instrumentation used in medical diagnosis and research.</p>
          </div>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Subject 1 -->
          <div class="subject-card bg-gray-50 rounded-xl p-6 border border-gray-200">
            <div class="flex items-start justify-between mb-4">
              <div class="text-2xl">🧲</div>
              <span class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Intermediate
              </span>
            </div>
            <h4 class="text-lg font-semibold text-gray-900 mb-2">MRI Fundamentals</h4>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">
              Magnetic Resonance Imaging principles, physics, and clinical applications.
            </p>
            <div class="mb-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">📅 8 weeks</span>
                <span class="text-sm font-medium text-blue-600">3 Credits</span>
              </div>

              <div class="mb-3">
                <p class="text-xs font-medium text-gray-700 mb-1">Prerequisites:</p>
                <div class="flex flex-wrap gap-1">
                  <span class="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded">Basic Physics</span>
                  <span class="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded">Mathematics</span>
                </div>
              </div>

              <div class="mb-3">
                <p class="text-xs font-medium text-gray-700 mb-1">Course Modules (6):</p>
                <div class="max-h-20 overflow-y-auto">
                  <ul class="text-xs text-gray-600 space-y-1">
                    <li class="flex items-start">
                      <span class="text-blue-500 mr-1">•</span>
                      MRI Physics and Magnetic Field Theory
                    </li>
                    <li class="flex items-start">
                      <span class="text-blue-500 mr-1">•</span>
                      Pulse Sequences and Signal Generation
                    </li>
                    <li class="flex items-start">
                      <span class="text-blue-500 mr-1">•</span>
                      Image Reconstruction Algorithms
                    </li>
                    <li class="text-gray-500 italic">+3 more modules...</li>
                  </ul>
                </div>
              </div>

              <button type="button" class="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded transition-colors">
                View Full Course Details
              </button>
            </div>
          </div>

          <!-- Subject 2 -->
          <div class="subject-card bg-gray-50 rounded-xl p-6 border border-gray-200">
            <div class="flex items-start justify-between mb-4">
              <div class="text-2xl">💻</div>
              <span class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Intermediate
              </span>
            </div>
            <h4 class="text-lg font-semibold text-gray-900 mb-2">CT Imaging Technology</h4>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">
              Computed Tomography systems, image reconstruction, and quality control.
            </p>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-500">📅 6 weeks</span>
              <button class="text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors">
                Learn More →
              </button>
            </div>
          </div>

          <!-- Subject 3 -->
          <div class="subject-card bg-gray-50 rounded-xl p-6 border border-gray-200">
            <div class="flex items-start justify-between mb-4">
              <div class="text-2xl">🌊</div>
              <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Beginner
              </span>
            </div>
            <h4 class="text-lg font-semibold text-gray-900 mb-2">Ultrasound Physics & Instrumentation</h4>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">
              Ultrasound wave physics, transducers, and imaging system components.
            </p>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-500">📅 7 weeks</span>
              <button class="text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors">
                Learn More →
              </button>
            </div>
          </div>

          <!-- Subject 4 -->
          <div class="subject-card bg-gray-50 rounded-xl p-6 border border-gray-200">
            <div class="flex items-start justify-between mb-4">
              <div class="text-2xl">☢️</div>
              <span class="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Advanced
              </span>
            </div>
            <h4 class="text-lg font-semibold text-gray-900 mb-2">Nuclear Medicine Imaging</h4>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">
              Radiopharmaceuticals, gamma cameras, and SPECT/PET imaging systems.
            </p>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-500">📅 10 weeks</span>
              <button class="text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors">
                Learn More →
              </button>
            </div>
          </div>

          <!-- Subject 5 -->
          <div class="subject-card bg-gray-50 rounded-xl p-6 border border-gray-200">
            <div class="flex items-start justify-between mb-4">
              <div class="text-2xl">📷</div>
              <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Beginner
              </span>
            </div>
            <h4 class="text-lg font-semibold text-gray-900 mb-2">Digital Radiography</h4>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">
              Digital X-ray systems, image processing, and PACS integration.
            </p>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-500">📅 5 weeks</span>
              <button class="text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors">
                Learn More →
              </button>
            </div>
          </div>

          <!-- Subject 6 -->
          <div class="subject-card bg-gray-50 rounded-xl p-6 border border-gray-200">
            <div class="flex items-start justify-between mb-4">
              <div class="text-2xl">🖼️</div>
              <span class="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Advanced
              </span>
            </div>
            <h4 class="text-lg font-semibold text-gray-900 mb-2">Medical Image Processing</h4>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">
              Image enhancement, segmentation, and analysis algorithms.
            </p>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-500">📅 9 weeks</span>
              <button class="text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors">
                Learn More →
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Call to Action -->
  <section class="bg-gray-900 text-white py-16">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-bold mb-6">
        Ready to Start Your Learning Journey?
      </h2>
      <p class="text-xl text-gray-300 mb-8">
        Join thousands of biomedical engineering students advancing their careers
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <button class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
          Track Your Progress
        </button>
        <button class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-lg font-semibold transition-colors">
          Discover More LMS
        </button>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-800 text-white py-6 text-center">
    <p>&copy; 2025 BioMed LMS. All rights reserved.</p>
    <p class="text-sm text-gray-400">Powered by AI</p>
  </footer>

  <script>
    function toggleDivision(divisionId) {
      // Simple demo functionality - in real app this would show different content
      console.log('Selected division:', divisionId);
      // You could add logic here to show different subject sets
    }
  </script>
</body>
</html>
