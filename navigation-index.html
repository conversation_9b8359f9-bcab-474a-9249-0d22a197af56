<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BioMed LMS - Navigation Hub</title>
  <link rel="icon" type="image/svg+xml" href="/public/favicon.svg">
  <meta name="description" content="Comprehensive biomedical learning management system navigation hub">
  <script src="https://cdn.tailwindcss.com"></script>

  <style>
    .nav-link {
      transition: all 0.2s ease;
    }
    .nav-link:hover {
      background-color: rgba(59, 130, 246, 0.1);
      transform: translateY(-1px);
    }
    .page-card {
      transition: all 0.3s ease;
    }
    .page-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    .gradient-bg {
      background: linear-gradient(135deg, #eff6ff 0%, #ffffff 50%, #f0fdf4 100%);
    }
    .category-card {
      transition: all 0.3s ease;
    }
    .category-card:hover {
      transform: scale(1.02);
    }
  </style>
</head>
<body class="bg-gray-50">
  <!-- Navigation Header -->
  <header class="bg-blue-700 text-white shadow-lg">
    <nav class="container mx-auto px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="text-3xl">🏥</div>
          <div>
            <h1 class="text-2xl font-bold">BioMed LMS</h1>
            <p class="text-blue-200 text-sm">Navigation Hub</p>
          </div>
        </div>
        <div class="hidden md:flex space-x-6">
          <a href="index.html" class="nav-link px-3 py-2 rounded-md text-sm font-medium bg-green-600 hover:bg-green-700 text-white">Main Landing</a>
          <a href="#main-pages" class="nav-link px-3 py-2 rounded-md text-sm font-medium">Main Pages</a>
          <a href="#course-modules" class="nav-link px-3 py-2 rounded-md text-sm font-medium">Course Modules</a>
          <a href="#react-app" class="nav-link px-3 py-2 rounded-md text-sm font-medium">React Application</a>
          <a href="#documentation" class="nav-link px-3 py-2 rounded-md text-sm font-medium">Documentation</a>
          <a href="sitemap.html" class="nav-link px-3 py-2 rounded-md text-sm font-medium bg-blue-600 hover:bg-blue-700">Site Map</a>
        </div>
      </div>
    </nav>
  </header>

  <!-- Hero Section -->
  <section class="gradient-bg py-16">
    <div class="container mx-auto px-6 text-center">
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
        Welcome to BioMed LMS
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
        Navigate through our comprehensive biomedical learning management system.
        Explore course modules, specializations, and educational resources designed for medical professionals.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="index.html" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
          Launch React Application
        </a>
        <a href="#main-pages" class="bg-white hover:bg-gray-50 text-blue-600 border-2 border-blue-600 px-8 py-3 rounded-lg font-semibold transition-colors">
          Browse Static Pages
        </a>
      </div>
    </div>
  </section>

  <!-- Main Pages Section -->
  <section id="main-pages" class="py-16 bg-white">
    <div class="container mx-auto px-6">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-bold text-gray-900 mb-4">Main Application Pages</h3>
        <p class="text-lg text-gray-600">Core pages of the BioMed LMS platform</p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
        <!-- Homepage Preview -->
        <div class="page-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-6 text-white">
            <div class="text-3xl mb-2">🏠</div>
            <h4 class="text-xl font-semibold">Homepage</h4>
            <p class="text-blue-100 text-sm">Main landing page with specialization selection</p>
          </div>
          <div class="p-6">
            <ul class="text-sm text-gray-600 space-y-2 mb-4">
              <li>• Hero section with call-to-action</li>
              <li>• Specialization preview cards</li>
              <li>• Statistics and features showcase</li>
              <li>• Interactive navigation</li>
            </ul>
            <div class="flex space-x-2">
              <a href="preview.html" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded text-sm font-medium transition-colors">
                View Preview
              </a>
              <a href="enhanced-homepage-preview.html" class="flex-1 bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded text-sm font-medium transition-colors">
                Enhanced Version
              </a>
            </div>
          </div>
        </div>

        <!-- Subject Categories -->
        <div class="page-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-purple-500 to-purple-600 p-6 text-white">
            <div class="text-3xl mb-2">📚</div>
            <h4 class="text-xl font-semibold">Subject Categories</h4>
            <p class="text-purple-100 text-sm">Three main biomedical engineering divisions</p>
          </div>
          <div class="p-6">
            <ul class="text-sm text-gray-600 space-y-2 mb-4">
              <li>• Biomedical Imaging Technology</li>
              <li>• Electrophysiological Instrumentation</li>
              <li>• Biomechanics & Rehabilitation</li>
              <li>• Interactive course selection</li>
            </ul>
            <a href="subject-categories-preview.html" class="block w-full bg-purple-600 hover:bg-purple-700 text-white text-center py-2 px-4 rounded text-sm font-medium transition-colors">
              View Categories
            </a>
          </div>
        </div>

        <!-- Course Details -->
        <div class="page-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-green-500 to-green-600 p-6 text-white">
            <div class="text-3xl mb-2">🔬</div>
            <h4 class="text-xl font-semibold">Course Details</h4>
            <p class="text-green-100 text-sm">Comprehensive course module breakdown</p>
          </div>
          <div class="p-6">
            <ul class="text-sm text-gray-600 space-y-2 mb-4">
              <li>• Detailed module descriptions</li>
              <li>• Learning outcomes & prerequisites</li>
              <li>• Assessment methods</li>
              <li>• Career path information</li>
            </ul>
            <a href="enhanced-course-details-preview.html" class="block w-full bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded text-sm font-medium transition-colors">
              View Course Details
            </a>
          </div>
        </div>

        <!-- Original Landing Page -->
        <div class="page-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-teal-500 to-teal-600 p-6 text-white">
            <div class="text-3xl mb-2">🌟</div>
            <h4 class="text-xl font-semibold">Original Landing</h4>
            <p class="text-teal-100 text-sm">Original BioMed LMS landing page</p>
          </div>
          <div class="p-6">
            <ul class="text-sm text-gray-600 space-y-2 mb-4">
              <li>• Three main course divisions</li>
              <li>• Interactive course exploration</li>
              <li>• Professional design</li>
              <li>• Contact information</li>
            </ul>
            <a href="BioMed LMS.html" class="block w-full bg-teal-600 hover:bg-teal-700 text-white text-center py-2 px-4 rounded text-sm font-medium transition-colors">
              View Original Page
            </a>
          </div>
        </div>

        <!-- Site Map -->
        <div class="page-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 p-6 text-white">
            <div class="text-3xl mb-2">🗺️</div>
            <h4 class="text-xl font-semibold">Site Map</h4>
            <p class="text-indigo-100 text-sm">Complete navigation overview</p>
          </div>
          <div class="p-6">
            <ul class="text-sm text-gray-600 space-y-2 mb-4">
              <li>• All pages and links</li>
              <li>• Navigation structure</li>
              <li>• Direct specialization access</li>
              <li>• Documentation links</li>
            </ul>
            <a href="sitemap.html" class="block w-full bg-indigo-600 hover:bg-indigo-700 text-white text-center py-2 px-4 rounded text-sm font-medium transition-colors">
              View Site Map
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Course Modules Section -->
  <section id="course-modules" class="py-16 bg-gray-50">
    <div class="container mx-auto px-6">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-bold text-gray-900 mb-4">Course Module Categories</h3>
        <p class="text-lg text-gray-600">Explore detailed curricula for each specialization</p>
      </div>

      <div class="grid md:grid-cols-3 gap-8">
        <!-- Biomedical Imaging -->
        <div class="category-card bg-white rounded-xl shadow-lg p-8 border border-gray-200">
          <div class="text-center mb-6">
            <div class="text-4xl mb-4">🔬</div>
            <h4 class="text-xl font-semibold text-gray-900 mb-2">Biomedical Imaging</h4>
            <p class="text-gray-600 text-sm">Advanced imaging technologies and instrumentation</p>
          </div>
          <div class="space-y-3">
            <div class="bg-blue-50 p-3 rounded-lg">
              <h5 class="font-medium text-blue-900">MRI Fundamentals</h5>
              <p class="text-blue-700 text-sm">3 credits • 8 weeks • 6 modules</p>
            </div>
            <div class="bg-blue-50 p-3 rounded-lg">
              <h5 class="font-medium text-blue-900">CT Imaging Technology</h5>
              <p class="text-blue-700 text-sm">3 credits • 6 weeks • 6 modules</p>
            </div>
            <div class="bg-blue-50 p-3 rounded-lg">
              <h5 class="font-medium text-blue-900">+ 4 More Courses</h5>
              <p class="text-blue-700 text-sm">Ultrasound, Nuclear Medicine, Digital Radiography, Image Processing</p>
            </div>
          </div>
          <div class="mt-6">
            <a href="subject-categories-preview.html?specialization=biomedical-imaging" class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded font-medium transition-colors">
              Explore Imaging Courses
            </a>
          </div>
        </div>

        <!-- Electrophysiological -->
        <div class="category-card bg-white rounded-xl shadow-lg p-8 border border-gray-200">
          <div class="text-center mb-6">
            <div class="text-4xl mb-4">⚡</div>
            <h4 class="text-xl font-semibold text-gray-900 mb-2">Electrophysiological</h4>
            <p class="text-gray-600 text-sm">Electrical activity measurement and analysis</p>
          </div>
          <div class="space-y-3">
            <div class="bg-green-50 p-3 rounded-lg">
              <h5 class="font-medium text-green-900">ECG Fundamentals</h5>
              <p class="text-green-700 text-sm">3 credits • 6 weeks • 6 modules</p>
            </div>
            <div class="bg-green-50 p-3 rounded-lg">
              <h5 class="font-medium text-green-900">EEG Signal Analysis</h5>
              <p class="text-green-700 text-sm">4 credits • 8 weeks • 8 modules</p>
            </div>
            <div class="bg-green-50 p-3 rounded-lg">
              <h5 class="font-medium text-green-900">+ 4 More Courses</h5>
              <p class="text-green-700 text-sm">EMG, Bioamplifiers, Signal Conditioning, Neural Interfaces</p>
            </div>
          </div>
          <div class="mt-6">
            <a href="subject-categories-preview.html?specialization=electrophysiological" class="block w-full bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded font-medium transition-colors">
              Explore Electrophysiology
            </a>
          </div>
        </div>

        <!-- Biomechanics -->
        <div class="category-card bg-white rounded-xl shadow-lg p-8 border border-gray-200">
          <div class="text-center mb-6">
            <div class="text-4xl mb-4">🦴</div>
            <h4 class="text-xl font-semibold text-gray-900 mb-2">Biomechanics & Rehab</h4>
            <p class="text-gray-600 text-sm">Mechanical principles and rehabilitation engineering</p>
          </div>
          <div class="space-y-3">
            <div class="bg-purple-50 p-3 rounded-lg">
              <h5 class="font-medium text-purple-900">Gait Analysis</h5>
              <p class="text-purple-700 text-sm">4 credits • 8 weeks • 8 modules</p>
            </div>
            <div class="bg-purple-50 p-3 rounded-lg">
              <h5 class="font-medium text-purple-900">Prosthetics Design</h5>
              <p class="text-purple-700 text-sm">4 credits • 10 weeks • 8 modules</p>
            </div>
            <div class="bg-purple-50 p-3 rounded-lg">
              <h5 class="font-medium text-purple-900">+ 4 More Courses</h5>
              <p class="text-purple-700 text-sm">Orthotics, Biomaterials, Rehab Robotics, Modeling</p>
            </div>
          </div>
          <div class="mt-6">
            <a href="subject-categories-preview.html?specialization=biomechanics-rehab" class="block w-full bg-purple-600 hover:bg-purple-700 text-white text-center py-2 px-4 rounded font-medium transition-colors">
              Explore Biomechanics
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- React Application Section -->
  <section id="react-app" class="py-16 bg-white">
    <div class="container mx-auto px-6">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-bold text-gray-900 mb-4">React Application</h3>
        <p class="text-lg text-gray-600">Full-featured interactive application with routing and state management</p>
      </div>

      <div class="max-w-4xl mx-auto">
        <div class="bg-gradient-to-r from-blue-600 to-green-600 rounded-xl p-8 text-white text-center">
          <div class="text-5xl mb-4">⚛️</div>
          <h4 class="text-2xl font-bold mb-4">Interactive BioMed LMS Application</h4>
          <p class="text-blue-100 mb-6 text-lg">
            Experience the full-featured React application with dynamic routing, state management,
            and interactive course selection. Built with TypeScript, React Router, and Tailwind CSS.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="index.html" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Launch React App
            </a>
            <a href="#documentation" class="bg-transparent border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
              View Documentation
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Documentation Section -->
  <section id="documentation" class="py-16 bg-gray-50">
    <div class="container mx-auto px-6">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-bold text-gray-900 mb-4">Documentation & Resources</h3>
        <p class="text-lg text-gray-600">Project documentation, setup guides, and technical resources</p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow-md p-6 text-center">
          <div class="text-3xl mb-3">📋</div>
          <h4 class="font-semibold text-gray-900 mb-2">Project Structure</h4>
          <p class="text-gray-600 text-sm mb-4">Detailed folder organization and architecture</p>
          <a href="PROJECT_STRUCTURE.md" class="text-blue-600 hover:text-blue-800 font-medium text-sm">
            View Structure →
          </a>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 text-center">
          <div class="text-3xl mb-3">✅</div>
          <h4 class="font-semibold text-gray-900 mb-2">Setup Complete</h4>
          <p class="text-gray-600 text-sm mb-4">Implementation summary and features</p>
          <a href="SETUP_COMPLETE.md" class="text-blue-600 hover:text-blue-800 font-medium text-sm">
            View Summary →
          </a>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 text-center">
          <div class="text-3xl mb-3">🎨</div>
          <h4 class="font-semibold text-gray-900 mb-2">CSS Assets</h4>
          <p class="text-gray-600 text-sm mb-4">Custom stylesheets and design system</p>
          <a href="assets/css/main.css" class="text-blue-600 hover:text-blue-800 font-medium text-sm">
            View Styles →
          </a>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 text-center">
          <div class="text-3xl mb-3">⚙️</div>
          <h4 class="font-semibold text-gray-900 mb-2">JS Utilities</h4>
          <p class="text-gray-600 text-sm mb-4">JavaScript helper functions and utilities</p>
          <a href="assets/js/utils.js" class="text-blue-600 hover:text-blue-800 font-medium text-sm">
            View Utils →
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-800 text-white py-8">
    <div class="container mx-auto px-6">
      <div class="grid md:grid-cols-3 gap-8">
        <div>
          <div class="flex items-center space-x-2 mb-4">
            <div class="text-2xl">🏥</div>
            <h5 class="text-lg font-semibold">BioMed LMS</h5>
          </div>
          <p class="text-gray-400 text-sm">
            Comprehensive biomedical learning management system for medical education and professional development.
          </p>
        </div>

        <div>
          <h5 class="text-lg font-semibold mb-4">Quick Links</h5>
          <ul class="space-y-2 text-sm">
            <li><a href="#main-pages" class="text-gray-400 hover:text-white transition-colors">Main Pages</a></li>
            <li><a href="#course-modules" class="text-gray-400 hover:text-white transition-colors">Course Modules</a></li>
            <li><a href="#react-app" class="text-gray-400 hover:text-white transition-colors">React Application</a></li>
            <li><a href="#documentation" class="text-gray-400 hover:text-white transition-colors">Documentation</a></li>
          </ul>
        </div>

        <div>
          <h5 class="text-lg font-semibold mb-4">Specializations</h5>
          <ul class="space-y-2 text-sm">
            <li><a href="subject-categories-preview.html?specialization=biomedical-imaging" class="text-gray-400 hover:text-white transition-colors">Biomedical Imaging</a></li>
            <li><a href="subject-categories-preview.html?specialization=electrophysiological" class="text-gray-400 hover:text-white transition-colors">Electrophysiological</a></li>
            <li><a href="subject-categories-preview.html?specialization=biomechanics-rehab" class="text-gray-400 hover:text-white transition-colors">Biomechanics & Rehab</a></li>
          </ul>
        </div>
      </div>

      <div class="border-t border-gray-700 mt-8 pt-8 text-center">
        <p class="text-gray-400 text-sm">&copy; 2025 BioMed LMS. All rights reserved. | Powered by AI</p>
      </div>
    </div>
  </footer>

  <script>
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Add active state to navigation links
    window.addEventListener('scroll', () => {
      const sections = document.querySelectorAll('section[id]');
      const navLinks = document.querySelectorAll('nav a[href^="#"]');

      let current = '';
      sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
          current = section.getAttribute('id');
        }
      });

      navLinks.forEach(link => {
        link.classList.remove('bg-blue-600');
        if (link.getAttribute('href') === `#${current}`) {
          link.classList.add('bg-blue-600');
        }
      });
    });
  </script>
</body>
</html>
