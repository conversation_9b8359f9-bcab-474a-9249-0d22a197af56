<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>تحليل الحركة البشرية - BioMed LMS</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="/assets/css/main.css">
  <style>
    .motion-animation {
      animation: walkCycle 2s infinite ease-in-out;
    }
    @keyframes walkCycle {
      0%, 100% { transform: translateX(0) rotate(0deg); }
      25% { transform: translateX(10px) rotate(2deg); }
      50% { transform: translateX(20px) rotate(0deg); }
      75% { transform: translateX(10px) rotate(-2deg); }
    }
    .force-vector {
      animation: forceFlow 1.5s infinite ease-in-out;
    }
    @keyframes forceFlow {
      0%, 100% { opacity: 0.7; transform: scale(1); }
      50% { opacity: 1; transform: scale(1.1); }
    }
    .joint-marker {
      animation: jointPulse 2s infinite ease-in-out;
    }
    @keyframes jointPulse {
      0%, 100% { transform: scale(1); opacity: 0.8; }
      50% { transform: scale(1.2); opacity: 1; }
    }
    .gait-cycle {
      stroke-dasharray: 1000;
      stroke-dashoffset: 1000;
      animation: drawGait 3s ease-in-out infinite;
    }
    @keyframes drawGait {
      0% { stroke-dashoffset: 1000; }
      50% { stroke-dashoffset: 0; }
      100% { stroke-dashoffset: -1000; }
    }
    .interactive-skeleton {
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .interactive-skeleton:hover {
      filter: brightness(1.2);
      transform: scale(1.02);
    }
    .bone-segment {
      transition: all 0.3s ease;
    }
    .bone-segment:hover {
      stroke-width: 4;
      filter: drop-shadow(0 0 5px #3b82f6);
    }
    .muscle-group {
      transition: all 0.3s ease;
      cursor: pointer;
    }
    .muscle-group:hover {
      fill-opacity: 0.8;
      transform: scale(1.05);
    }
    .biomech-chart {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }
    .data-point {
      animation: dataFlow 2s infinite ease-in-out;
    }
    @keyframes dataFlow {
      0%, 100% { r: 3; opacity: 0.7; }
      50% { r: 5; opacity: 1; }
    }
  </style>
</head>
<body class="bg-gray-50 font-sans">
  <!-- Navigation -->
  <nav class="bg-white shadow-lg sticky top-0 z-50">
    <div class="container mx-auto px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4 space-x-reverse">
          <div class="text-2xl motion-animation">🏃</div>
          <div>
            <h1 class="text-xl font-bold text-gray-900">تحليل الحركة البشرية</h1>
            <p class="text-sm text-gray-500">Human Motion Analysis Course</p>
          </div>
        </div>

        <div class="flex items-center space-x-4 space-x-reverse">
          <a href="biomechanics-rehabilitation-courses.html" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">العودة للتخصص</a>
          <a href="courses-index.html" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">فهرس المواد</a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Course Progress -->
  <div class="bg-purple-600 text-white py-4">
    <div class="container mx-auto px-6">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium">تقدم المحاضرة</span>
        <span class="text-sm" id="progress-text">1 / 5</span>
      </div>
      <div class="w-full bg-purple-800 rounded-full h-2">
        <div class="progress-bar bg-white h-2 rounded-full" style="width: 20%" id="progress-bar"></div>
      </div>
    </div>
  </div>

  <!-- Module Navigation -->
  <div class="bg-white border-b shadow-sm sticky top-20 z-40">
    <div class="container mx-auto px-6 py-3">
      <div class="flex flex-wrap gap-2">
        <button type="button" class="module-btn px-4 py-2 text-sm rounded-lg bg-purple-100 text-purple-800 font-medium" data-module="1">
          1. أساسيات الميكانيكا الحيوية
        </button>
        <button type="button" class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="2">
          2. تحليل المشي والجري
        </button>
        <button type="button" class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="3">
          3. القوى والعزوم
        </button>
        <button type="button" class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="4">
          4. تحليل العضلات والمفاصل
        </button>
        <button type="button" class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="5">
          5. التطبيقات السريرية
        </button>
      </div>
    </div>
  </div>

  <!-- Lecture Slides -->
  <section class="py-8">
    <div class="container mx-auto px-6">

      <!-- Slide 1: Biomechanics Fundamentals -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content active" id="slide-1">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4 motion-animation">🏃</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">أساسيات الميكانيكا الحيوية</h2>
          <p class="text-lg text-gray-600">Biomechanics Fundamentals</p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">الهيكل العظمي البشري</h3>

            <!-- Human Skeleton Diagram -->
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
              <svg width="100%" height="400" viewBox="0 0 300 400" class="interactive-skeleton">
                <!-- Head -->
                <circle cx="150" cy="40" r="25" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" class="bone-segment" data-bone="skull" onclick="highlightBone('skull')"/>

                <!-- Spine -->
                <line x1="150" y1="65" x2="150" y2="200" stroke="#8b5cf6" stroke-width="6" class="bone-segment" data-bone="spine" onclick="highlightBone('spine')"/>

                <!-- Arms -->
                <g class="arm-left">
                  <line x1="150" y1="100" x2="100" y2="120" stroke="#3b82f6" stroke-width="4" class="bone-segment" data-bone="humerus-l" onclick="highlightBone('humerus')"/>
                  <line x1="100" y1="120" x2="80" y2="160" stroke="#3b82f6" stroke-width="4" class="bone-segment" data-bone="forearm-l" onclick="highlightBone('forearm')"/>
                  <circle cx="80" cy="160" r="8" fill="#ef4444" class="joint-marker" data-joint="wrist-l"/>
                </g>

                <g class="arm-right">
                  <line x1="150" y1="100" x2="200" y2="120" stroke="#3b82f6" stroke-width="4" class="bone-segment" data-bone="humerus-r" onclick="highlightBone('humerus')"/>
                  <line x1="200" y1="120" x2="220" y2="160" stroke="#3b82f6" stroke-width="4" class="bone-segment" data-bone="forearm-r" onclick="highlightBone('forearm')"/>
                  <circle cx="220" cy="160" r="8" fill="#ef4444" class="joint-marker" data-joint="wrist-r"/>
                </g>

                <!-- Pelvis -->
                <ellipse cx="150" cy="200" rx="40" ry="15" fill="#fbbf24" stroke="#f59e0b" stroke-width="2" class="bone-segment" data-bone="pelvis" onclick="highlightBone('pelvis')"/>

                <!-- Legs -->
                <g class="leg-left">
                  <line x1="130" y1="215" x2="120" y2="280" stroke="#10b981" stroke-width="5" class="bone-segment" data-bone="femur-l" onclick="highlightBone('femur')"/>
                  <line x1="120" y1="280" x2="115" y2="340" stroke="#10b981" stroke-width="5" class="bone-segment" data-bone="tibia-l" onclick="highlightBone('tibia')"/>
                  <circle cx="120" cy="280" r="10" fill="#ef4444" class="joint-marker" data-joint="knee-l"/>
                  <circle cx="115" cy="340" r="8" fill="#ef4444" class="joint-marker" data-joint="ankle-l"/>
                </g>

                <g class="leg-right">
                  <line x1="170" y1="215" x2="180" y2="280" stroke="#10b981" stroke-width="5" class="bone-segment" data-bone="femur-r" onclick="highlightBone('femur')"/>
                  <line x1="180" y1="280" x2="185" y2="340" stroke="#10b981" stroke-width="5" class="bone-segment" data-bone="tibia-r" onclick="highlightBone('tibia')"/>
                  <circle cx="180" cy="280" r="10" fill="#ef4444" class="joint-marker" data-joint="knee-r"/>
                  <circle cx="185" cy="340" r="8" fill="#ef4444" class="joint-marker" data-joint="ankle-r"/>
                </g>

                <!-- Major Joints -->
                <circle cx="150" cy="100" r="12" fill="#ef4444" class="joint-marker" data-joint="shoulder"/>
                <circle cx="150" cy="200" r="12" fill="#ef4444" class="joint-marker" data-joint="hip"/>

                <!-- Labels -->
                <text x="50" y="50" class="text-xs font-medium">الجمجمة</text>
                <text x="160" y="150" class="text-xs font-medium">العمود الفقري</text>
                <text x="200" y="250" class="text-xs font-medium">عظم الفخذ</text>
              </svg>
            </div>

            <div class="space-y-3">
              <div class="interactive-demo p-3 rounded-lg cursor-pointer" onclick="animateSkeleton()">
                <div class="flex items-center">
                  <span class="text-2xl ml-3">🦴</span>
                  <div>
                    <h4 class="font-semibold text-gray-900">محاكاة الحركة</h4>
                    <p class="text-sm text-gray-600">انقر لرؤية حركة المفاصل</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">المبادئ الأساسية</h3>

            <div class="space-y-4">
              <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">الكينماتيكا</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• وصف الحركة دون اعتبار القوى</li>
                  <li>• الموضع، السرعة، التسارع</li>
                  <li>• الحركة الخطية والزاوية</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">الكينتيكا</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• دراسة القوى المسببة للحركة</li>
                  <li>• قوانين نيوتن للحركة</li>
                  <li>• القوى الداخلية والخارجية</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">الاستاتيكا</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• توازن القوى والعزوم</li>
                  <li>• تحليل الوضعيات الثابتة</li>
                  <li>• مركز الثقل والاستقرار</li>
                </ul>
              </div>
            </div>

            <div class="mt-6 bg-yellow-50 rounded-lg p-4">
              <h4 class="font-semibold text-gray-900 mb-2">المعادلات الأساسية</h4>
              <div class="space-y-2 text-sm">
                <div class="bg-white p-2 rounded font-mono text-center">
                  F = ma
                </div>
                <div class="bg-white p-2 rounded font-mono text-center">
                  τ = r × F
                </div>
                <div class="bg-white p-2 rounded font-mono text-center">
                  ΣF = 0, Στ = 0
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Force Vectors Demonstration -->
        <div class="mt-8 bg-gray-50 rounded-lg p-6">
          <h4 class="font-semibold text-gray-900 mb-4">تمثيل القوى والعزوم</h4>
          <svg width="100%" height="200" viewBox="0 0 600 200" class="force-demo">
            <!-- Ground -->
            <line x1="0" y1="180" x2="600" y2="180" stroke="#8b5cf6" stroke-width="3"/>

            <!-- Person standing -->
            <g class="person-figure">
              <circle cx="300" cy="50" r="20" fill="#fbbf24"/>
              <line x1="300" y1="70" x2="300" y2="140" stroke="#3b82f6" stroke-width="4"/>
              <line x1="300" y1="140" x2="280" y2="180" stroke="#10b981" stroke-width="4"/>
              <line x1="300" y1="140" x2="320" y2="180" stroke="#10b981" stroke-width="4"/>
            </g>

            <!-- Force vectors -->
            <g class="force-vectors">
              <!-- Weight -->
              <line x1="300" y1="100" x2="300" y2="160" stroke="#ef4444" stroke-width="3" marker-end="url(#arrowRed)" class="force-vector"/>
              <text x="310" y="130" class="text-sm font-medium">W (الوزن)</text>

              <!-- Ground reaction forces -->
              <line x1="280" y1="180" x2="280" y2="140" stroke="#10b981" stroke-width="3" marker-end="url(#arrowGreen)" class="force-vector"/>
              <line x1="320" y1="180" x2="320" y2="140" stroke="#10b981" stroke-width="3" marker-end="url(#arrowGreen)" class="force-vector"/>
              <text x="250" y="195" class="text-sm font-medium">رد فعل الأرض</text>
            </g>

            <!-- Arrow markers -->
            <defs>
              <marker id="arrowRed" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#ef4444"/>
              </marker>
              <marker id="arrowGreen" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#10b981"/>
              </marker>
            </defs>
          </svg>
        </div>
      </div>

      <!-- Slide 2: Gait Analysis -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content" id="slide-2" style="display: none;">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4">🚶</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">تحليل المشي والجري</h2>
          <p class="text-lg text-gray-600">Gait Analysis</p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">دورة المشي</h3>

            <!-- Gait Cycle Diagram -->
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
              <svg width="100%" height="300" viewBox="0 0 500 300" class="gait-diagram">
                <!-- Timeline -->
                <line x1="50" y1="250" x2="450" y2="250" stroke="#374151" stroke-width="2"/>

                <!-- Stance Phase -->
                <rect x="50" y="200" width="200" height="30" fill="#3b82f6" fill-opacity="0.3" stroke="#3b82f6" stroke-width="2"/>
                <text x="150" y="220" text-anchor="middle" class="text-sm font-medium">Stance Phase (60%)</text>

                <!-- Swing Phase -->
                <rect x="250" y="200" width="200" height="30" fill="#10b981" fill-opacity="0.3" stroke="#10b981" stroke-width="2"/>
                <text x="350" y="220" text-anchor="middle" class="text-sm font-medium">Swing Phase (40%)</text>

                <!-- Gait Events -->
                <g class="gait-events">
                  <!-- Heel Strike -->
                  <line x1="50" y1="180" x2="50" y2="250" stroke="#ef4444" stroke-width="3"/>
                  <text x="50" y="175" text-anchor="middle" class="text-xs">Heel Strike</text>

                  <!-- Toe Off -->
                  <line x1="250" y1="180" x2="250" y2="250" stroke="#f59e0b" stroke-width="3"/>
                  <text x="250" y="175" text-anchor="middle" class="text-xs">Toe Off</text>

                  <!-- Next Heel Strike -->
                  <line x1="450" y1="180" x2="450" y2="250" stroke="#ef4444" stroke-width="3"/>
                  <text x="450" y="175" text-anchor="middle" class="text-xs">Next Heel Strike</text>
                </g>

                <!-- Foot positions -->
                <g class="foot-positions">
                  <!-- Initial contact -->
                  <ellipse cx="50" cy="120" rx="25" ry="8" fill="#3b82f6" class="interactive-foot" onclick="highlightGaitPhase('initial')"/>
                  <text x="50" y="105" text-anchor="middle" class="text-xs">Initial Contact</text>

                  <!-- Mid stance -->
                  <ellipse cx="150" cy="120" rx="25" ry="8" fill="#1d4ed8" class="interactive-foot" onclick="highlightGaitPhase('midstance')"/>
                  <text x="150" y="105" text-anchor="middle" class="text-xs">Mid Stance</text>

                  <!-- Pre-swing -->
                  <ellipse cx="250" cy="120" rx="25" ry="8" fill="#2563eb" class="interactive-foot" onclick="highlightGaitPhase('preswing')"/>
                  <text x="250" y="105" text-anchor="middle" class="text-xs">Pre-swing</text>

                  <!-- Mid swing -->
                  <ellipse cx="350" cy="80" rx="25" ry="8" fill="#10b981" class="interactive-foot" onclick="highlightGaitPhase('midswing')"/>
                  <text x="350" y="65" text-anchor="middle" class="text-xs">Mid Swing</text>
                </g>

                <!-- Animated gait cycle line -->
                <path d="M 50 120 Q 150 100 250 120 Q 350 60 450 120" stroke="#8b5cf6" stroke-width="3" fill="none" class="gait-cycle"/>
              </svg>
            </div>

            <div class="space-y-3">
              <div class="interactive-demo p-3 rounded-lg cursor-pointer" onclick="animateGaitCycle()">
                <div class="flex items-center">
                  <span class="text-2xl ml-3">🔄</span>
                  <div>
                    <h4 class="font-semibold text-gray-900">محاكاة دورة المشي</h4>
                    <p class="text-sm text-gray-600">انقر لرؤية الحركة المتكاملة</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">معاملات المشي</h3>

            <div class="space-y-4">
              <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">المعاملات الزمنية</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• زمن الدورة: 1.0-1.2 ثانية</li>
                  <li>• زمن الوقوف: 60% من الدورة</li>
                  <li>• زمن التأرجح: 40% من الدورة</li>
                  <li>• معدل الخطوات: 110-120 خطوة/دقيقة</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">المعاملات المكانية</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• طول الخطوة: 70-80 سم</li>
                  <li>• عرض الخطوة: 8-12 سم</li>
                  <li>• زاوية القدم: 5-18 درجة</li>
                  <li>• السرعة: 1.2-1.4 م/ث</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">المعاملات الحركية</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• حركة الورك: 40° انثناء/تمديد</li>
                  <li>• حركة الركبة: 60° انثناء/تمديد</li>
                  <li>• حركة الكاحل: 30° ظهرية/أخمصية</li>
                </ul>
              </div>
            </div>

            <!-- Real-time Gait Data -->
            <div class="mt-6 bg-gray-50 rounded-lg p-4">
              <h4 class="font-semibold text-gray-900 mb-4">بيانات المشي الفورية</h4>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div class="bg-white p-3 rounded text-center">
                  <div class="text-lg font-bold text-blue-600" id="step-length">75</div>
                  <div class="text-gray-600">طول الخطوة (سم)</div>
                </div>
                <div class="bg-white p-3 rounded text-center">
                  <div class="text-lg font-bold text-green-600" id="cadence">115</div>
                  <div class="text-gray-600">معدل الخطوات</div>
                </div>
                <div class="bg-white p-3 rounded text-center">
                  <div class="text-lg font-bold text-purple-600" id="velocity">1.3</div>
                  <div class="text-gray-600">السرعة (م/ث)</div>
                </div>
                <div class="bg-white p-3 rounded text-center">
                  <div class="text-lg font-bold text-orange-600" id="stance-time">62</div>
                  <div class="text-gray-600">زمن الوقوف (%)</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Slide 3: Forces and Moments -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content" id="slide-3" style="display: none;">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4">⚡</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">القوى والعزوم</h2>
          <p class="text-lg text-gray-600">Forces and Moments</p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">تحليل القوى</h3>

            <!-- Force Platform Data -->
            <div class="bg-gray-50 rounded-lg p-6 mb-6 biomech-chart">
              <h4 class="font-semibold text-gray-900 mb-4">منصة القوى - Ground Reaction Forces</h4>
              <svg width="100%" height="250" viewBox="0 0 400 250" class="force-chart">
                <!-- Grid -->
                <defs>
                  <pattern id="force-grid" width="40" height="25" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 25" fill="none" stroke="#e5e7eb" stroke-width="1"/>
                  </pattern>
                </defs>
                <rect width="400" height="250" fill="url(#force-grid)"/>

                <!-- Axes -->
                <line x1="40" y1="200" x2="360" y2="200" stroke="#374151" stroke-width="2"/>
                <line x1="40" y1="200" x2="40" y2="40" stroke="#374151" stroke-width="2"/>

                <!-- Force curves -->
                <!-- Vertical force -->
                <path d="M 40 200 Q 80 180 120 120 Q 160 100 200 110 Q 240 120 280 140 Q 320 160 360 200"
                      stroke="#ef4444" stroke-width="3" fill="none" class="force-curve vertical-force"/>

                <!-- Anterior-Posterior force -->
                <path d="M 40 200 Q 80 190 120 210 Q 160 220 200 200 Q 240 180 280 190 Q 320 195 360 200"
                      stroke="#3b82f6" stroke-width="3" fill="none" class="force-curve ap-force"/>

                <!-- Medial-Lateral force -->
                <path d="M 40 200 Q 80 205 120 195 Q 160 190 200 200 Q 240 205 280 195 Q 320 200 360 200"
                      stroke="#10b981" stroke-width="3" fill="none" class="force-curve ml-force"/>

                <!-- Data points -->
                <circle cx="120" cy="120" r="4" fill="#ef4444" class="data-point"/>
                <circle cx="200" cy="110" r="4" fill="#ef4444" class="data-point"/>
                <circle cx="280" cy="140" r="4" fill="#ef4444" class="data-point"/>

                <!-- Labels -->
                <text x="50" y="60" class="text-xs font-medium">القوة (N)</text>
                <text x="320" y="220" class="text-xs font-medium">الزمن (s)</text>

                <!-- Legend -->
                <g class="legend">
                  <line x1="50" y1="30" x2="70" y2="30" stroke="#ef4444" stroke-width="3"/>
                  <text x="75" y="35" class="text-xs">عمودية</text>

                  <line x1="120" y1="30" x2="140" y2="30" stroke="#3b82f6" stroke-width="3"/>
                  <text x="145" y="35" class="text-xs">أمامية-خلفية</text>

                  <line x1="220" y1="30" x2="240" y2="30" stroke="#10b981" stroke-width="3"/>
                  <text x="245" y="35" class="text-xs">جانبية</text>
                </g>
              </svg>
            </div>

            <div class="space-y-3">
              <div class="interactive-demo p-3 rounded-lg cursor-pointer" onclick="animateForceData()">
                <div class="flex items-center">
                  <span class="text-2xl ml-3">📊</span>
                  <div>
                    <h4 class="font-semibold text-gray-900">محاكاة بيانات القوى</h4>
                    <p class="text-sm text-gray-600">انقر لرؤية تغير القوى مع الزمن</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">العزوم المفصلية</h3>

            <!-- Joint Moments Diagram -->
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
              <svg width="100%" height="200" viewBox="0 0 300 200" class="joint-moments">
                <!-- Leg outline -->
                <g class="leg-diagram">
                  <!-- Thigh -->
                  <line x1="150" y1="50" x2="130" y2="100" stroke="#8b5cf6" stroke-width="6"/>
                  <!-- Shin -->
                  <line x1="130" y1="100" x2="140" y2="150" stroke="#8b5cf6" stroke-width="6"/>
                  <!-- Foot -->
                  <line x1="140" y1="150" x2="170" y2="150" stroke="#8b5cf6" stroke-width="6"/>

                  <!-- Joints -->
                  <circle cx="150" cy="50" r="8" fill="#ef4444" class="joint-hip"/>
                  <circle cx="130" cy="100" r="8" fill="#ef4444" class="joint-knee"/>
                  <circle cx="140" cy="150" r="8" fill="#ef4444" class="joint-ankle"/>

                  <!-- Moment arrows -->
                  <g class="moment-arrows">
                    <!-- Hip moment -->
                    <path d="M 160 45 A 15 15 0 0 1 160 55" stroke="#f59e0b" stroke-width="3" fill="none" marker-end="url(#arrowOrange)"/>
                    <text x="175" y="52" class="text-xs font-medium">M_hip</text>

                    <!-- Knee moment -->
                    <path d="M 140 95 A 15 15 0 0 1 140 105" stroke="#06b6d4" stroke-width="3" fill="none" marker-end="url(#arrowCyan)"/>
                    <text x="155" y="102" class="text-xs font-medium">M_knee</text>

                    <!-- Ankle moment -->
                    <path d="M 150 145 A 15 15 0 0 1 150 155" stroke="#84cc16" stroke-width="3" fill="none" marker-end="url(#arrowLime)"/>
                    <text x="165" y="152" class="text-xs font-medium">M_ankle</text>
                  </g>
                </g>

                <!-- Arrow markers -->
                <defs>
                  <marker id="arrowOrange" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                    <polygon points="0 0, 8 3, 0 6" fill="#f59e0b"/>
                  </marker>
                  <marker id="arrowCyan" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                    <polygon points="0 0, 8 3, 0 6" fill="#06b6d4"/>
                  </marker>
                  <marker id="arrowLime" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                    <polygon points="0 0, 8 3, 0 6" fill="#84cc16"/>
                  </marker>
                </defs>
              </svg>
            </div>

            <div class="space-y-4">
              <div class="bg-gradient-to-r from-orange-50 to-orange-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">عزم الورك</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• العضلات: الألوية والمثنيات</li>
                  <li>• الوظيفة: الدعم والدفع</li>
                  <li>• القيمة القصوى: 1.5 Nm/kg</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-cyan-50 to-cyan-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">عزم الركبة</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• العضلات: الرباعية والمأبضية</li>
                  <li>• الوظيفة: امتصاص الصدمات</li>
                  <li>• القيمة القصوى: 1.0 Nm/kg</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-lime-50 to-lime-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">عزم الكاحل</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• العضلات: الربلة والظنبوبية</li>
                  <li>• الوظيفة: الدفع للأمام</li>
                  <li>• القيمة القصوى: 1.8 Nm/kg</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation Controls -->
      <div class="flex items-center justify-between mt-8">
        <button type="button" id="prev-btn" class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
          ← السابق
        </button>

        <div class="flex items-center space-x-2 space-x-reverse">
          <span class="w-3 h-3 bg-purple-600 rounded-full slide-indicator active" data-slide="1"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="2"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="3"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="4"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="5"></span>
        </div>

        <button type="button" id="next-btn" class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
          التالي →
        </button>
      </div>
    </div>
  </section>

  <!-- Tooltip -->
  <div id="tooltip" class="fixed bg-gray-900 text-white p-3 rounded-lg shadow-lg z-50 opacity-0 pointer-events-none transition-opacity duration-200">
    <div id="tooltip-content"></div>
  </div>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-8">
    <div class="container mx-auto px-6 text-center">
      <p>&copy; 2025 BioMed LMS. جميع الحقوق محفوظة.</p>
    </div>
  </footer>

  <script src="/assets/js/utils.js"></script>
  <script>
    let currentSlide = 1;
    const totalSlides = 5;

    // Slide navigation functions
    function showSlide(slideNumber) {
      document.querySelectorAll('.slide-content').forEach(slide => {
        slide.style.display = 'none';
        slide.classList.remove('active');
      });

      const currentSlideEl = document.getElementById(`slide-${slideNumber}`);
      if (currentSlideEl) {
        currentSlideEl.style.display = 'block';
        setTimeout(() => {
          currentSlideEl.classList.add('active');
        }, 50);
      }

      updateProgress(slideNumber);
      updateNavigation(slideNumber);
      updateModuleButtons(slideNumber);
    }

    function updateProgress(slideNumber) {
      const progress = (slideNumber / totalSlides) * 100;
      document.getElementById('progress-bar').style.width = `${progress}%`;
      document.getElementById('progress-text').textContent = `${slideNumber} / ${totalSlides}`;
    }

    function updateNavigation(slideNumber) {
      const prevBtn = document.getElementById('prev-btn');
      const nextBtn = document.getElementById('next-btn');

      prevBtn.disabled = slideNumber === 1;
      nextBtn.disabled = slideNumber === totalSlides;

      document.querySelectorAll('.slide-indicator').forEach((indicator, index) => {
        if (index + 1 === slideNumber) {
          indicator.classList.add('active', 'bg-purple-600');
          indicator.classList.remove('bg-gray-300');
        } else {
          indicator.classList.remove('active', 'bg-purple-600');
          indicator.classList.add('bg-gray-300');
        }
      });
    }

    function updateModuleButtons(slideNumber) {
      document.querySelectorAll('.module-btn').forEach((btn, index) => {
        if (index + 1 === slideNumber) {
          btn.classList.remove('bg-gray-100', 'text-gray-600');
          btn.classList.add('bg-purple-100', 'text-purple-800', 'font-medium');
        } else {
          btn.classList.remove('bg-purple-100', 'text-purple-800', 'font-medium');
          btn.classList.add('bg-gray-100', 'text-gray-600');
        }
      });
    }

    // Interactive functions
    function highlightBone(boneType) {
      const bones = {
        skull: { name: 'الجمجمة', function: 'حماية الدماغ', joints: 'مفاصل ثابتة' },
        spine: { name: 'العمود الفقري', function: 'الدعم والحركة', joints: '24 فقرة متحركة' },
        humerus: { name: 'عظم العضد', function: 'حركة الذراع', joints: 'الكتف والمرفق' },
        forearm: { name: 'عظام الساعد', function: 'حركة اليد والمعصم', joints: 'المرفق والمعصم' },
        pelvis: { name: 'الحوض', function: 'نقل الوزن للأرجل', joints: 'مفصل الورك' },
        femur: { name: 'عظم الفخذ', function: 'أقوى عظم في الجسم', joints: 'الورك والركبة' },
        tibia: { name: 'عظم الساق', function: 'تحمل وزن الجسم', joints: 'الركبة والكاحل' }
      };

      if (bones[boneType]) {
        const bone = bones[boneType];
        showTooltip(`${bone.name}: ${bone.function} - ${bone.joints}`);

        // Highlight the bone
        const boneElements = document.querySelectorAll(`[data-bone*="${boneType}"]`);
        boneElements.forEach(element => {
          element.style.filter = 'brightness(1.3)';
          element.style.transform = 'scale(1.05)';
          setTimeout(() => {
            element.style.filter = 'brightness(1)';
            element.style.transform = 'scale(1)';
          }, 1500);
        });
      }
    }

    function animateSkeleton() {
      const skeleton = document.querySelector('.interactive-skeleton');
      const joints = document.querySelectorAll('.joint-marker');

      // Animate skeleton
      skeleton.style.animation = 'none';
      setTimeout(() => {
        skeleton.style.animation = 'walkCycle 2s ease-in-out 3';
      }, 10);

      // Pulse joints
      joints.forEach((joint, index) => {
        setTimeout(() => {
          joint.style.animation = 'jointPulse 0.5s ease-in-out 2';
        }, index * 100);
      });

      showTooltip('محاكاة حركة المفاصل والعظام أثناء المشي');
    }

    function showTooltip(message) {
      const tooltip = document.getElementById('tooltip');
      const content = document.getElementById('tooltip-content');

      content.textContent = message;
      tooltip.style.opacity = '1';

      if (event) {
        tooltip.style.left = event.pageX + 10 + 'px';
        tooltip.style.top = event.pageY - 40 + 'px';
      }

      setTimeout(() => {
        tooltip.style.opacity = '0';
      }, 3000);
    }

    // Gait analysis functions
    function highlightGaitPhase(phase) {
      const phases = {
        initial: { name: 'الاتصال الأولي', description: 'لحظة ملامسة الكعب للأرض', percentage: '0-2%' },
        midstance: { name: 'منتصف الوقوف', description: 'الجسم فوق القدم الداعمة', percentage: '10-30%' },
        preswing: { name: 'ما قبل التأرجح', description: 'رفع أصابع القدم عن الأرض', percentage: '50-60%' },
        midswing: { name: 'منتصف التأرجح', description: 'القدم في أعلى نقطة', percentage: '73-87%' }
      };

      if (phases[phase]) {
        const phaseInfo = phases[phase];
        showTooltip(`${phaseInfo.name}: ${phaseInfo.description} (${phaseInfo.percentage})`);

        // Highlight the phase
        const phaseElement = document.querySelector(`[onclick="highlightGaitPhase('${phase}')"]`);
        if (phaseElement) {
          phaseElement.style.transform = 'scale(1.2)';
          phaseElement.style.filter = 'brightness(1.3)';
          setTimeout(() => {
            phaseElement.style.transform = 'scale(1)';
            phaseElement.style.filter = 'brightness(1)';
          }, 1500);
        }
      }
    }

    function animateGaitCycle() {
      const gaitLine = document.querySelector('.gait-cycle');
      const footPositions = document.querySelectorAll('.interactive-foot');
      const gaitData = document.querySelectorAll('[id^="step-"], [id^="cadence"], [id^="velocity"], [id^="stance-"]');

      // Animate the gait cycle line
      gaitLine.style.animation = 'none';
      setTimeout(() => {
        gaitLine.style.animation = 'drawGait 3s ease-in-out 1';
      }, 10);

      // Animate foot positions
      footPositions.forEach((foot, index) => {
        setTimeout(() => {
          foot.style.transform = 'scale(1.3)';
          foot.style.filter = 'brightness(1.2)';
          setTimeout(() => {
            foot.style.transform = 'scale(1)';
            foot.style.filter = 'brightness(1)';
          }, 500);
        }, index * 400);
      });

      // Update gait parameters with random variations
      setTimeout(() => {
        const stepLength = document.getElementById('step-length');
        const cadence = document.getElementById('cadence');
        const velocity = document.getElementById('velocity');
        const stanceTime = document.getElementById('stance-time');

        if (stepLength) stepLength.textContent = (70 + Math.random() * 15).toFixed(0);
        if (cadence) cadence.textContent = (110 + Math.random() * 15).toFixed(0);
        if (velocity) velocity.textContent = (1.2 + Math.random() * 0.4).toFixed(1);
        if (stanceTime) stanceTime.textContent = (58 + Math.random() * 8).toFixed(0);
      }, 1000);

      showTooltip('محاكاة دورة المشي الكاملة مع تحديث البيانات الفورية');
    }

    // Force analysis functions
    function animateForceData() {
      const forceCurves = document.querySelectorAll('.force-curve');
      const dataPoints = document.querySelectorAll('.data-point');

      // Animate force curves
      forceCurves.forEach((curve, index) => {
        curve.style.strokeDasharray = '1000';
        curve.style.strokeDashoffset = '1000';

        setTimeout(() => {
          curve.style.animation = 'drawCurve 2s ease-in-out forwards';
        }, index * 300);
      });

      // Animate data points
      dataPoints.forEach((point, index) => {
        setTimeout(() => {
          point.style.animation = 'dataFlow 1s ease-in-out 3';
        }, 1000 + index * 200);
      });

      showTooltip('محاكاة بيانات قوى رد فعل الأرض أثناء المشي');
    }

    // Joint moment analysis
    function highlightJoint(jointType) {
      const joints = {
        hip: { name: 'مفصل الورك', muscles: 'الألوية والمثنيات', maxMoment: '1.5 Nm/kg' },
        knee: { name: 'مفصل الركبة', muscles: 'الرباعية والمأبضية', maxMoment: '1.0 Nm/kg' },
        ankle: { name: 'مفصل الكاحل', muscles: 'الربلة والظنبوبية', maxMoment: '1.8 Nm/kg' }
      };

      if (joints[jointType]) {
        const joint = joints[jointType];
        showTooltip(`${joint.name}: ${joint.muscles} - العزم الأقصى: ${joint.maxMoment}`);

        // Highlight the joint
        const jointElement = document.querySelector(`.joint-${jointType}`);
        if (jointElement) {
          jointElement.style.transform = 'scale(1.5)';
          jointElement.style.filter = 'brightness(1.3)';
          setTimeout(() => {
            jointElement.style.transform = 'scale(1)';
            jointElement.style.filter = 'brightness(1)';
          }, 1500);
        }
      }
    }

    // Event listeners
    document.getElementById('next-btn').addEventListener('click', () => {
      if (currentSlide < totalSlides) {
        currentSlide++;
        showSlide(currentSlide);
      }
    });

    document.getElementById('prev-btn').addEventListener('click', () => {
      if (currentSlide > 1) {
        currentSlide--;
        showSlide(currentSlide);
      }
    });

    document.querySelectorAll('.module-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const moduleNumber = parseInt(btn.dataset.module);
        currentSlide = moduleNumber;
        showSlide(currentSlide);
      });
    });

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      showSlide(1);
    });
  </script>
</body>
</html>
