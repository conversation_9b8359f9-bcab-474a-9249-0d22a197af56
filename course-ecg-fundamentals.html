<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>أساسيات تخطيط القلب الكهربائي - BioMed LMS</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="/assets/css/main.css">
  <style>
    .ecg-wave {
      animation: ecgPulse 2s infinite;
    }
    @keyframes ecgPulse {
      0%, 100% { transform: scaleY(1); }
      50% { transform: scaleY(1.2); }
    }
    .heartbeat {
      animation: heartbeat 1.5s infinite;
    }
    @keyframes heartbeat {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.1); }
    }
    .electrode-placement {
      position: relative;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .electrode-placement:hover {
      transform: scale(1.1);
      filter: brightness(1.2);
    }
    .signal-trace {
      stroke-dasharray: 1000;
      stroke-dashoffset: 1000;
      animation: drawTrace 3s ease-in-out infinite;
    }
    @keyframes drawTrace {
      to { stroke-dashoffset: 0; }
    }
    .interactive-demo {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border: 2px dashed #0ea5e9;
      transition: all 0.3s ease;
    }
    .interactive-demo:hover {
      background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
      border-color: #0284c7;
    }
    .interactive-component {
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .interactive-component:hover {
      filter: brightness(1.1);
      transform: scale(1.02);
    }
    .interactive-wave {
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .interactive-wave:hover {
      stroke-width: 4 !important;
      filter: brightness(1.2);
    }
    .interactive-label {
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .interactive-label:hover {
      font-size: 16px;
      font-weight: bold;
    }
    @keyframes drawCurve {
      to { stroke-dashoffset: 0; }
    }
    @keyframes drawWave {
      to { stroke-dashoffset: 0; }
    }
    .circuit-stage {
      transition: all 0.3s ease;
    }
    .signal-flow {
      transition: all 0.3s ease;
    }
    .wave-explanation {
      transition: all 0.3s ease;
    }
  </style>
</head>
<body class="bg-gray-50 font-sans">
  <!-- Navigation -->
  <nav class="bg-white shadow-lg sticky top-0 z-50">
    <div class="container mx-auto px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4 space-x-reverse">
          <div class="text-2xl heartbeat">💓</div>
          <div>
            <h1 class="text-xl font-bold text-gray-900">أساسيات تخطيط القلب الكهربائي</h1>
            <p class="text-sm text-gray-500">ECG Fundamentals Course</p>
          </div>
        </div>

        <div class="flex items-center space-x-4 space-x-reverse">
          <a href="electrophysiological-courses.html" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">العودة للتخصص</a>
          <a href="courses-index.html" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">فهرس المواد</a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Course Progress -->
  <div class="bg-green-600 text-white py-4">
    <div class="container mx-auto px-6">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium">تقدم المحاضرة</span>
        <span class="text-sm" id="progress-text">1 / 4</span>
      </div>
      <div class="w-full bg-green-800 rounded-full h-2">
        <div class="progress-bar bg-white h-2 rounded-full" style="width: 25%" id="progress-bar"></div>
      </div>
    </div>
  </div>

  <!-- Module Navigation -->
  <div class="bg-white border-b shadow-sm sticky top-20 z-40">
    <div class="container mx-auto px-6 py-3">
      <div class="flex flex-wrap gap-2">
        <button class="module-btn px-4 py-2 text-sm rounded-lg bg-green-100 text-green-800 font-medium" data-module="1">
          1. فيزيولوجيا القلب الكهربائية
        </button>
        <button class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="2">
          2. أقطاب ECG وتقنيات التوصيل
        </button>
        <button class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="3">
          3. مكبرات الإشارات الحيوية
        </button>
        <button class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="4">
          4. تحليل إشارات ECG
        </button>
      </div>
    </div>
  </div>

  <!-- Lecture Slides -->
  <section class="py-8">
    <div class="container mx-auto px-6">

      <!-- Slide 1: Heart Electrophysiology -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content active" id="slide-1">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4 heartbeat">💓</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">فيزيولوجيا القلب الكهربائية</h2>
          <p class="text-lg text-gray-600">Cardiac Electrophysiology</p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">النظام الكهربائي للقلب</h3>

            <div class="space-y-4">
              <div class="interactive-demo p-4 rounded-lg cursor-pointer" onclick="highlightNode('sa')">
                <div class="flex items-center">
                  <span class="text-3xl ml-4">🔴</span>
                  <div>
                    <h4 class="font-semibold text-gray-900">العقدة الجيبية الأذينية (SA Node)</h4>
                    <p class="text-sm text-gray-600">منظم ضربات القلب الطبيعي - 60-100 نبضة/دقيقة</p>
                  </div>
                </div>
              </div>

              <div class="interactive-demo p-4 rounded-lg cursor-pointer" onclick="highlightNode('av')">
                <div class="flex items-center">
                  <span class="text-3xl ml-4">🟡</span>
                  <div>
                    <h4 class="font-semibold text-gray-900">العقدة الأذينية البطينية (AV Node)</h4>
                    <p class="text-sm text-gray-600">تأخير الإشارة - 0.1-0.2 ثانية</p>
                  </div>
                </div>
              </div>

              <div class="interactive-demo p-4 rounded-lg cursor-pointer" onclick="highlightNode('bundle')">
                <div class="flex items-center">
                  <span class="text-3xl ml-4">🔵</span>
                  <div>
                    <h4 class="font-semibold text-gray-900">حزمة هيس والألياف</h4>
                    <p class="text-sm text-gray-600">توصيل سريع للبطينين</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-6">
            <h4 class="font-semibold text-gray-900 mb-4">دورة القلب الكهربائية</h4>
            <div id="heart-diagram" class="text-center">
              <div class="relative w-48 h-48 mx-auto bg-white rounded-full shadow-lg flex items-center justify-center">
                <div class="text-6xl heartbeat">❤️</div>
                <div class="absolute top-4 right-8 w-3 h-3 bg-red-500 rounded-full electrode-placement" data-node="sa" title="SA Node"></div>
                <div class="absolute bottom-8 left-8 w-3 h-3 bg-yellow-500 rounded-full electrode-placement" data-node="av" title="AV Node"></div>
                <div class="absolute bottom-4 right-12 w-3 h-3 bg-blue-500 rounded-full electrode-placement" data-node="bundle" title="Bundle of His"></div>
              </div>
            </div>
            <p class="text-sm text-gray-600 mt-4 text-center">انقر على النقاط الملونة لاستكشاف النظام الكهربائي</p>
          </div>
        </div>

        <div class="mt-8 bg-blue-50 rounded-lg p-6">
          <h4 class="text-lg font-semibold text-gray-900 mb-4">جهد العمل القلبي</h4>
          <div class="grid md:grid-cols-3 gap-4">
            <div class="bg-white p-4 rounded-lg text-center">
              <div class="text-2xl mb-2">⚡</div>
              <div class="font-medium">المرحلة 0</div>
              <div class="text-sm text-gray-600">إزالة الاستقطاب السريع</div>
            </div>
            <div class="bg-white p-4 rounded-lg text-center">
              <div class="text-2xl mb-2">📈</div>
              <div class="font-medium">المرحلة 2</div>
              <div class="text-sm text-gray-600">الهضبة</div>
            </div>
            <div class="bg-white p-4 rounded-lg text-center">
              <div class="text-2xl mb-2">📉</div>
              <div class="font-medium">المرحلة 3</div>
              <div class="text-sm text-gray-600">إعادة الاستقطاب</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Slide 2: ECG Electrodes -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content" id="slide-2" style="display: none;">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4">🔌</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">أقطاب ECG وتقنيات التوصيل</h2>
          <p class="text-lg text-gray-600">ECG Electrodes and Lead Systems</p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">نظام الـ 12 قطب</h3>

            <div class="space-y-4">
              <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">الأقطاب الطرفية (Limb Leads)</h4>
                <div class="grid grid-cols-3 gap-2 text-sm">
                  <div class="bg-white p-2 rounded text-center">I</div>
                  <div class="bg-white p-2 rounded text-center">II</div>
                  <div class="bg-white p-2 rounded text-center">III</div>
                  <div class="bg-white p-2 rounded text-center">aVR</div>
                  <div class="bg-white p-2 rounded text-center">aVL</div>
                  <div class="bg-white p-2 rounded text-center">aVF</div>
                </div>
              </div>

              <div class="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">الأقطاب الصدرية (Precordial Leads)</h4>
                <div class="grid grid-cols-3 gap-2 text-sm">
                  <div class="bg-white p-2 rounded text-center">V1</div>
                  <div class="bg-white p-2 rounded text-center">V2</div>
                  <div class="bg-white p-2 rounded text-center">V3</div>
                  <div class="bg-white p-2 rounded text-center">V4</div>
                  <div class="bg-white p-2 rounded text-center">V5</div>
                  <div class="bg-white p-2 rounded text-center">V6</div>
                </div>
              </div>
            </div>

            <div class="mt-6">
              <button type="button" class="w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors" onclick="simulateECGPlacement()">
                🎯 محاكاة وضع الأقطاب
              </button>
            </div>
          </div>

          <div class="bg-gray-50 rounded-lg p-6">
            <h4 class="font-semibold text-gray-900 mb-4">مخطط وضع الأقطاب</h4>
            <div id="electrode-placement" class="text-center">
              <div class="relative w-64 h-80 mx-auto bg-gradient-to-b from-blue-100 to-blue-200 rounded-lg shadow-lg">
                <!-- Body outline -->
                <div class="absolute inset-4 border-2 border-gray-400 rounded-lg"></div>

                <!-- Electrode positions -->
                <div class="absolute top-8 left-4 w-4 h-4 bg-red-500 rounded-full electrode-placement" data-lead="RA" title="Right Arm"></div>
                <div class="absolute top-8 right-4 w-4 h-4 bg-yellow-500 rounded-full electrode-placement" data-lead="LA" title="Left Arm"></div>
                <div class="absolute bottom-8 left-4 w-4 h-4 bg-green-500 rounded-full electrode-placement" data-lead="RL" title="Right Leg"></div>
                <div class="absolute bottom-8 right-4 w-4 h-4 bg-blue-500 rounded-full electrode-placement" data-lead="LL" title="Left Leg"></div>

                <!-- Precordial leads -->
                <div class="absolute top-16 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-purple-500 rounded-full electrode-placement" data-lead="V1" title="V1"></div>
                <div class="absolute top-20 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-purple-500 rounded-full electrode-placement" data-lead="V2" title="V2"></div>
                <div class="absolute top-24 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-purple-500 rounded-full electrode-placement" data-lead="V3" title="V3"></div>
                <div class="absolute top-28 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-purple-500 rounded-full electrode-placement" data-lead="V4" title="V4"></div>
                <div class="absolute top-32 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-purple-500 rounded-full electrode-placement" data-lead="V5" title="V5"></div>
                <div class="absolute top-36 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-purple-500 rounded-full electrode-placement" data-lead="V6" title="V6"></div>
              </div>
            </div>
            <p class="text-sm text-gray-600 mt-4 text-center">انقر على "محاكاة وضع الأقطاب" لرؤية التوضيح التفاعلي</p>
          </div>
        </div>
      </div>

      <!-- Slide 3: Bioamplifiers -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content" id="slide-3" style="display: none;">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4">📡</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">مكبرات الإشارات الحيوية</h2>
          <p class="text-lg text-gray-600">Bioamplifiers & Signal Conditioning</p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">مخطط الدائرة الأساسية</h3>

            <!-- Circuit Diagram -->
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
              <svg width="100%" height="300" viewBox="0 0 400 300" class="circuit-diagram">
                <!-- Input Stage -->
                <g class="circuit-stage" data-stage="input">
                  <rect x="20" y="120" width="60" height="40" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5" class="interactive-component" onclick="highlightStage('input')"/>
                  <text x="50" y="145" text-anchor="middle" class="text-xs font-medium">Input Buffer</text>

                  <!-- Input connections -->
                  <line x1="10" y1="130" x2="20" y2="130" stroke="#333" stroke-width="2"/>
                  <line x1="10" y1="150" x2="20" y2="150" stroke="#333" stroke-width="2"/>
                  <text x="5" y="128" class="text-xs">+</text>
                  <text x="5" y="155" class="text-xs">-</text>
                </g>

                <!-- Amplification Stage -->
                <g class="circuit-stage" data-stage="amp">
                  <polygon points="120,100 120,180 200,140" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2" class="interactive-component" onclick="highlightStage('amp')"/>
                  <text x="150" y="145" text-anchor="middle" class="text-xs font-medium">Amp</text>

                  <!-- Gain control -->
                  <rect x="140" y="90" width="20" height="10" fill="#fff" stroke="#7b1fa2" stroke-width="1"/>
                  <text x="150" y="87" text-anchor="middle" class="text-xs">Gain</text>

                  <!-- Connection from input -->
                  <line x1="80" y1="140" x2="120" y2="140" stroke="#333" stroke-width="2"/>
                </g>

                <!-- Filter Stage -->
                <g class="circuit-stage" data-stage="filter">
                  <rect x="220" y="120" width="60" height="40" fill="#e8f5e8" stroke="#388e3c" stroke-width="2" rx="5" class="interactive-component" onclick="highlightStage('filter')"/>
                  <text x="250" y="145" text-anchor="middle" class="text-xs font-medium">Filter</text>

                  <!-- Filter components -->
                  <circle cx="235" cy="110" r="3" fill="#388e3c"/>
                  <circle cx="250" cy="110" r="3" fill="#388e3c"/>
                  <circle cx="265" cy="110" r="3" fill="#388e3c"/>

                  <!-- Connection from amp -->
                  <line x1="200" y1="140" x2="220" y2="140" stroke="#333" stroke-width="2"/>
                </g>

                <!-- Output Stage -->
                <g class="circuit-stage" data-stage="output">
                  <rect x="300" y="120" width="60" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5" class="interactive-component" onclick="highlightStage('output')"/>
                  <text x="330" y="145" text-anchor="middle" class="text-xs font-medium">Output</text>

                  <!-- Connection from filter -->
                  <line x1="280" y1="140" x2="300" y2="140" stroke="#333" stroke-width="2"/>

                  <!-- Output connection -->
                  <line x1="360" y1="140" x2="380" y2="140" stroke="#333" stroke-width="2"/>
                  <text x="385" y="145" class="text-xs">Out</text>
                </g>

                <!-- Signal flow arrows -->
                <defs>
                  <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                  </marker>
                </defs>

                <path d="M 90 120 Q 105 110 115 120" stroke="#666" stroke-width="1" fill="none" marker-end="url(#arrowhead)" class="signal-flow"/>
                <path d="M 210 120 Q 215 110 225 120" stroke="#666" stroke-width="1" fill="none" marker-end="url(#arrowhead)" class="signal-flow"/>
                <path d="M 290 120 Q 295 110 305 120" stroke="#666" stroke-width="1" fill="none" marker-end="url(#arrowhead)" class="signal-flow"/>
              </svg>
            </div>

            <div class="space-y-3">
              <div class="interactive-demo p-3 rounded-lg cursor-pointer" onclick="simulateSignalFlow()">
                <div class="flex items-center">
                  <span class="text-2xl ml-3">🌊</span>
                  <div>
                    <h4 class="font-semibold text-gray-900">محاكاة تدفق الإشارة</h4>
                    <p class="text-sm text-gray-600">انقر لرؤية مسار الإشارة عبر الدائرة</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">مواصفات التصميم</h3>

            <div class="space-y-4">
              <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">مرحلة الدخل</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• مقاومة دخل عالية (>10 MΩ)</li>
                  <li>• مقاومة دخل مشتركة منخفضة</li>
                  <li>• حماية من الجهد الزائد</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">مرحلة التكبير</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• كسب قابل للتعديل (100-10000)</li>
                  <li>• نسبة رفض الإشارة المشتركة عالية</li>
                  <li>• ضوضاء منخفضة (<5 μV)</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">مرحلة التصفية</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• مرشح تمرير عالي (0.05-1 Hz)</li>
                  <li>• مرشح تمرير منخفض (100-1000 Hz)</li>
                  <li>• مرشح Notch (50/60 Hz)</li>
                </ul>
              </div>
            </div>

            <div class="mt-6">
              <button type="button" class="w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors" onclick="showFrequencyResponse()">
                📊 عرض استجابة التردد
              </button>
            </div>
          </div>
        </div>

        <!-- Frequency Response Chart -->
        <div id="frequency-chart" class="mt-8 bg-gray-50 rounded-lg p-6" style="display: none;">
          <h4 class="font-semibold text-gray-900 mb-4">استجابة التردد للمكبر الحيوي</h4>
          <svg width="100%" height="200" viewBox="0 0 600 200" class="frequency-response">
            <!-- Grid -->
            <defs>
              <pattern id="grid" width="50" height="20" patternUnits="userSpaceOnUse">
                <path d="M 50 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1"/>
              </pattern>
            </defs>
            <rect width="600" height="200" fill="url(#grid)"/>

            <!-- Axes -->
            <line x1="50" y1="150" x2="550" y2="150" stroke="#333" stroke-width="2"/>
            <line x1="50" y1="150" x2="50" y2="30" stroke="#333" stroke-width="2"/>

            <!-- Frequency response curve -->
            <path d="M 50 140 Q 100 120 150 100 L 400 100 Q 450 110 500 130 L 550 140"
                  stroke="#2196f3" stroke-width="3" fill="none" class="response-curve"/>

            <!-- Frequency labels -->
            <text x="100" y="170" text-anchor="middle" class="text-xs">0.1 Hz</text>
            <text x="200" y="170" text-anchor="middle" class="text-xs">1 Hz</text>
            <text x="300" y="170" text-anchor="middle" class="text-xs">10 Hz</text>
            <text x="400" y="170" text-anchor="middle" class="text-xs">100 Hz</text>
            <text x="500" y="170" text-anchor="middle" class="text-xs">1 kHz</text>

            <!-- Gain labels -->
            <text x="40" y="50" text-anchor="middle" class="text-xs">40 dB</text>
            <text x="40" y="100" text-anchor="middle" class="text-xs">20 dB</text>
            <text x="40" y="150" text-anchor="middle" class="text-xs">0 dB</text>
          </svg>
        </div>
      </div>

      <!-- Slide 4: ECG Signal Analysis -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content" id="slide-4" style="display: none;">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4">📈</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">تحليل إشارات ECG</h2>
          <p class="text-lg text-gray-600">ECG Signal Analysis</p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">مكونات إشارة ECG</h3>

            <!-- ECG Waveform -->
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
              <svg width="100%" height="250" viewBox="0 0 400 250" class="ecg-waveform">
                <!-- Grid -->
                <defs>
                  <pattern id="ecg-grid" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#ffcdd2" stroke-width="0.5"/>
                  </pattern>
                </defs>
                <rect width="400" height="250" fill="url(#ecg-grid)"/>

                <!-- Baseline -->
                <line x1="0" y1="125" x2="400" y2="125" stroke="#666" stroke-width="1" stroke-dasharray="2,2"/>

                <!-- ECG Complex -->
                <g class="ecg-complex">
                  <!-- P wave -->
                  <path d="M 50 125 Q 70 115 90 125" stroke="#e91e63" stroke-width="3" fill="none" class="interactive-wave" data-wave="p" onclick="highlightWave('p')"/>

                  <!-- QRS complex -->
                  <path d="M 120 125 L 130 135 L 140 85 L 150 155 L 160 125" stroke="#2196f3" stroke-width="3" fill="none" class="interactive-wave" data-wave="qrs" onclick="highlightWave('qrs')"/>

                  <!-- T wave -->
                  <path d="M 190 125 Q 210 105 230 125" stroke="#4caf50" stroke-width="3" fill="none" class="interactive-wave" data-wave="t" onclick="highlightWave('t')"/>

                  <!-- Second complex -->
                  <path d="M 270 125 Q 290 115 310 125" stroke="#e91e63" stroke-width="2" fill="none" class="interactive-wave" data-wave="p2"/>
                  <path d="M 320 125 L 325 130 L 330 90 L 335 150 L 340 125" stroke="#2196f3" stroke-width="2" fill="none" class="interactive-wave" data-wave="qrs2"/>
                  <path d="M 350 125 Q 365 110 380 125" stroke="#4caf50" stroke-width="2" fill="none" class="interactive-wave" data-wave="t2"/>
                </g>

                <!-- Labels -->
                <text x="70" y="105" text-anchor="middle" class="text-sm font-medium interactive-label" onclick="highlightWave('p')">P</text>
                <text x="140" y="75" text-anchor="middle" class="text-sm font-medium interactive-label" onclick="highlightWave('qrs')">QRS</text>
                <text x="210" y="95" text-anchor="middle" class="text-sm font-medium interactive-label" onclick="highlightWave('t')">T</text>

                <!-- Intervals -->
                <g class="intervals">
                  <line x1="70" y1="200" x2="140" y2="200" stroke="#ff9800" stroke-width="2"/>
                  <text x="105" y="195" text-anchor="middle" class="text-xs">PR Interval</text>

                  <line x1="120" y1="220" x2="160" y2="220" stroke="#9c27b0" stroke-width="2"/>
                  <text x="140" y="235" text-anchor="middle" class="text-xs">QRS Duration</text>

                  <line x1="140" y1="180" x2="210" y2="180" stroke="#00bcd4" stroke-width="2"/>
                  <text x="175" y="175" text-anchor="middle" class="text-xs">ST Segment</text>
                </g>
              </svg>
            </div>

            <div class="space-y-3">
              <div class="interactive-demo p-3 rounded-lg cursor-pointer" onclick="animateECG()">
                <div class="flex items-center">
                  <span class="text-2xl ml-3">💓</span>
                  <div>
                    <h4 class="font-semibold text-gray-900">محاكاة نبضة القلب</h4>
                    <p class="text-sm text-gray-600">انقر لرؤية الإشارة المتحركة</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">تفسير الموجات</h3>

            <div class="space-y-4" id="wave-explanations">
              <div class="bg-gradient-to-r from-pink-50 to-pink-100 p-4 rounded-lg wave-explanation" data-wave="p">
                <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
                  <span class="w-4 h-4 bg-pink-500 rounded-full ml-2"></span>
                  موجة P
                </h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• إزالة استقطاب الأذينين</li>
                  <li>• المدة: 80-120 ms</li>
                  <li>• الارتفاع: <2.5 mm</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg wave-explanation" data-wave="qrs">
                <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
                  <span class="w-4 h-4 bg-blue-500 rounded-full ml-2"></span>
                  مركب QRS
                </h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• إزالة استقطاب البطينين</li>
                  <li>• المدة: 80-120 ms</li>
                  <li>• الارتفاع: متغير حسب القطب</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg wave-explanation" data-wave="t">
                <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
                  <span class="w-4 h-4 bg-green-500 rounded-full ml-2"></span>
                  موجة T
                </h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• إعادة استقطاب البطينين</li>
                  <li>• المدة: 160-200 ms</li>
                  <li>• الشكل: مقوس ومتماثل</li>
                </ul>
              </div>
            </div>

            <div class="mt-6 bg-yellow-50 rounded-lg p-4">
              <h4 class="font-semibold text-gray-900 mb-2">القيم الطبيعية</h4>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div class="font-medium">معدل القلب</div>
                  <div class="text-gray-600">60-100 نبضة/دقيقة</div>
                </div>
                <div>
                  <div class="font-medium">فترة PR</div>
                  <div class="text-gray-600">120-200 ms</div>
                </div>
                <div>
                  <div class="font-medium">مدة QRS</div>
                  <div class="text-gray-600">80-120 ms</div>
                </div>
                <div>
                  <div class="font-medium">فترة QT</div>
                  <div class="text-gray-600">350-450 ms</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation Controls -->
      <div class="flex items-center justify-between mt-8">
        <button type="button" id="prev-btn" class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
          ← السابق
        </button>

        <div class="flex items-center space-x-2 space-x-reverse">
          <span class="w-3 h-3 bg-green-600 rounded-full slide-indicator active" data-slide="1"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="2"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="3"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="4"></span>
        </div>

        <button type="button" id="next-btn" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
          التالي →
        </button>
      </div>
    </div>
  </section>

  <!-- Tooltip -->
  <div id="tooltip" class="fixed bg-gray-900 text-white p-3 rounded-lg shadow-lg z-50 opacity-0 pointer-events-none transition-opacity duration-200">
    <div id="tooltip-content"></div>
  </div>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-8">
    <div class="container mx-auto px-6 text-center">
      <p>&copy; 2025 BioMed LMS. جميع الحقوق محفوظة.</p>
    </div>
  </footer>

  <script src="/assets/js/utils.js"></script>
  <script>
    let currentSlide = 1;
    const totalSlides = 4;

    // Slide navigation functions
    function showSlide(slideNumber) {
      document.querySelectorAll('.slide-content').forEach(slide => {
        slide.style.display = 'none';
        slide.classList.remove('active');
      });

      const currentSlideEl = document.getElementById(`slide-${slideNumber}`);
      if (currentSlideEl) {
        currentSlideEl.style.display = 'block';
        setTimeout(() => {
          currentSlideEl.classList.add('active');
        }, 50);
      }

      updateProgress(slideNumber);
      updateNavigation(slideNumber);
      updateModuleButtons(slideNumber);
    }

    function updateProgress(slideNumber) {
      const progress = (slideNumber / totalSlides) * 100;
      document.getElementById('progress-bar').style.width = `${progress}%`;
      document.getElementById('progress-text').textContent = `${slideNumber} / ${totalSlides}`;
    }

    function updateNavigation(slideNumber) {
      const prevBtn = document.getElementById('prev-btn');
      const nextBtn = document.getElementById('next-btn');

      prevBtn.disabled = slideNumber === 1;
      nextBtn.disabled = slideNumber === totalSlides;

      document.querySelectorAll('.slide-indicator').forEach((indicator, index) => {
        if (index + 1 === slideNumber) {
          indicator.classList.add('active', 'bg-green-600');
          indicator.classList.remove('bg-gray-300');
        } else {
          indicator.classList.remove('active', 'bg-green-600');
          indicator.classList.add('bg-gray-300');
        }
      });
    }

    function updateModuleButtons(slideNumber) {
      document.querySelectorAll('.module-btn').forEach((btn, index) => {
        if (index + 1 === slideNumber) {
          btn.classList.remove('bg-gray-100', 'text-gray-600');
          btn.classList.add('bg-green-100', 'text-green-800', 'font-medium');
        } else {
          btn.classList.remove('bg-green-100', 'text-green-800', 'font-medium');
          btn.classList.add('bg-gray-100', 'text-gray-600');
        }
      });
    }

    // Interactive functions
    function highlightNode(nodeType) {
      const nodes = {
        sa: { name: 'العقدة الجيبية الأذينية', rate: '60-100 نبضة/دقيقة', function: 'منظم ضربات القلب الطبيعي' },
        av: { name: 'العقدة الأذينية البطينية', delay: '0.1-0.2 ثانية', function: 'تأخير وتنظيم الإشارة' },
        bundle: { name: 'حزمة هيس والألياف', speed: 'سريع', function: 'توصيل للبطينين' }
      };

      if (nodes[nodeType]) {
        const node = nodes[nodeType];
        showTooltip(`${node.name}: ${node.function}`);

        // Highlight the corresponding electrode
        const electrode = document.querySelector(`[data-node="${nodeType}"]`);
        if (electrode) {
          electrode.style.transform = 'scale(1.5)';
          electrode.style.boxShadow = '0 0 15px rgba(59, 130, 246, 0.8)';
          setTimeout(() => {
            electrode.style.transform = 'scale(1)';
            electrode.style.boxShadow = 'none';
          }, 1000);
        }
      }
    }

    // Circuit diagram interactions
    function highlightStage(stageType) {
      const stages = {
        input: { name: 'مرحلة الدخل', function: 'عزل وحماية الإشارة الحيوية', specs: 'مقاومة عالية >10MΩ' },
        amp: { name: 'مرحلة التكبير', function: 'تكبير الإشارة الضعيفة', specs: 'كسب 100-10000' },
        filter: { name: 'مرحلة التصفية', function: 'إزالة الضوضاء والتداخل', specs: 'مرشحات 0.05Hz-1kHz' },
        output: { name: 'مرحلة الخرج', function: 'تكييف الإشارة للعرض', specs: 'مقاومة منخفضة <100Ω' }
      };

      if (stages[stageType]) {
        const stage = stages[stageType];
        showTooltip(`${stage.name}: ${stage.function} - ${stage.specs}`);

        // Highlight the stage
        const stageElement = document.querySelector(`[data-stage="${stageType}"]`);
        if (stageElement) {
          const component = stageElement.querySelector('.interactive-component');
          if (component) {
            component.style.filter = 'brightness(1.2)';
            component.style.transform = 'scale(1.05)';
            setTimeout(() => {
              component.style.filter = 'brightness(1)';
              component.style.transform = 'scale(1)';
            }, 1000);
          }
        }
      }
    }

    // Signal flow simulation
    function simulateSignalFlow() {
      const stages = ['input', 'amp', 'filter', 'output'];
      const arrows = document.querySelectorAll('.signal-flow');

      stages.forEach((stage, index) => {
        setTimeout(() => {
          const stageElement = document.querySelector(`[data-stage="${stage}"]`);
          const component = stageElement.querySelector('.interactive-component');

          // Highlight current stage
          component.style.filter = 'brightness(1.3)';
          component.style.boxShadow = '0 0 15px rgba(33, 150, 243, 0.8)';

          // Animate arrow
          if (arrows[index]) {
            arrows[index].style.stroke = '#2196f3';
            arrows[index].style.strokeWidth = '3';
          }

          setTimeout(() => {
            component.style.filter = 'brightness(1)';
            component.style.boxShadow = 'none';
            if (arrows[index]) {
              arrows[index].style.stroke = '#666';
              arrows[index].style.strokeWidth = '1';
            }
          }, 800);
        }, index * 600);
      });

      showTooltip('تدفق الإشارة: دخل → تكبير → تصفية → خرج');
    }

    // Frequency response chart
    function showFrequencyResponse() {
      const chart = document.getElementById('frequency-chart');
      const curve = chart.querySelector('.response-curve');

      if (chart.style.display === 'none') {
        chart.style.display = 'block';

        // Animate the curve drawing
        curve.style.strokeDasharray = '1000';
        curve.style.strokeDashoffset = '1000';
        curve.style.animation = 'drawCurve 2s ease-in-out forwards';

        showTooltip('استجابة التردد تُظهر نطاق التشغيل الفعال للمكبر الحيوي');
      } else {
        chart.style.display = 'none';
      }
    }

    // ECG waveform interactions
    function highlightWave(waveType) {
      const waves = {
        p: { name: 'موجة P', function: 'إزالة استقطاب الأذينين', duration: '80-120 ms', color: '#e91e63' },
        qrs: { name: 'مركب QRS', function: 'إزالة استقطاب البطينين', duration: '80-120 ms', color: '#2196f3' },
        t: { name: 'موجة T', function: 'إعادة استقطاب البطينين', duration: '160-200 ms', color: '#4caf50' }
      };

      if (waves[waveType]) {
        const wave = waves[waveType];
        showTooltip(`${wave.name}: ${wave.function} - المدة: ${wave.duration}`);

        // Highlight the wave
        const waveElement = document.querySelector(`[data-wave="${waveType}"]`);
        if (waveElement) {
          waveElement.style.strokeWidth = '5';
          waveElement.style.filter = 'drop-shadow(0 0 5px ' + wave.color + ')';
          setTimeout(() => {
            waveElement.style.strokeWidth = '3';
            waveElement.style.filter = 'none';
          }, 1500);
        }

        // Highlight corresponding explanation
        const explanations = document.querySelectorAll('.wave-explanation');
        explanations.forEach(exp => exp.style.opacity = '0.5');

        const targetExp = document.querySelector(`[data-wave="${waveType}"]`);
        if (targetExp && targetExp.classList.contains('wave-explanation')) {
          targetExp.style.opacity = '1';
          targetExp.style.transform = 'scale(1.02)';
          setTimeout(() => {
            explanations.forEach(exp => exp.style.opacity = '1');
            targetExp.style.transform = 'scale(1)';
          }, 2000);
        }
      }
    }

    // Animate ECG signal
    function animateECG() {
      const complex = document.querySelector('.ecg-complex');
      const waves = complex.querySelectorAll('.interactive-wave');

      waves.forEach((wave, index) => {
        setTimeout(() => {
          wave.style.strokeDasharray = '1000';
          wave.style.strokeDashoffset = '1000';
          wave.style.animation = 'drawWave 0.5s ease-in-out forwards';

          setTimeout(() => {
            wave.style.strokeDasharray = 'none';
            wave.style.strokeDashoffset = '0';
            wave.style.animation = 'none';
          }, 500);
        }, index * 100);
      });

      showTooltip('محاكاة إشارة ECG في الزمن الحقيقي');
    }

    function simulateECGPlacement() {
      const electrodes = document.querySelectorAll('.electrode-placement');
      const button = event.target;

      button.disabled = true;
      button.textContent = '🔄 جاري المحاكاة...';

      electrodes.forEach((electrode, index) => {
        setTimeout(() => {
          electrode.style.transform = 'scale(1.3)';
          electrode.style.boxShadow = '0 0 10px rgba(34, 197, 94, 0.8)';

          setTimeout(() => {
            electrode.style.transform = 'scale(1)';
            electrode.style.boxShadow = 'none';
          }, 500);
        }, index * 300);
      });

      setTimeout(() => {
        button.disabled = false;
        button.textContent = '🎯 محاكاة وضع الأقطاب';
        showTooltip('تم وضع جميع الأقطاب بنجاح! الآن يمكن تسجيل إشارات ECG');
      }, electrodes.length * 300 + 500);
    }

    function showTooltip(message) {
      const tooltip = document.getElementById('tooltip');
      const content = document.getElementById('tooltip-content');

      content.textContent = message;
      tooltip.style.opacity = '1';

      if (event) {
        tooltip.style.left = event.pageX + 10 + 'px';
        tooltip.style.top = event.pageY - 40 + 'px';
      }

      setTimeout(() => {
        tooltip.style.opacity = '0';
      }, 3000);
    }

    // Event listeners
    document.getElementById('next-btn').addEventListener('click', () => {
      if (currentSlide < totalSlides) {
        currentSlide++;
        showSlide(currentSlide);
      }
    });

    document.getElementById('prev-btn').addEventListener('click', () => {
      if (currentSlide > 1) {
        currentSlide--;
        showSlide(currentSlide);
      }
    });

    document.querySelectorAll('.module-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const moduleNumber = parseInt(btn.dataset.module);
        currentSlide = moduleNumber;
        showSlide(currentSlide);
      });
    });

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      showSlide(1);
    });
  </script>
</body>
</html>
