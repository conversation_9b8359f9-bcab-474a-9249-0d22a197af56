<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>أساسيات تخطيط القلب الكهربائي - BioMed LMS</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="/assets/css/main.css">
  <style>
    .ecg-wave {
      animation: ecgPulse 2s infinite;
    }
    @keyframes ecgPulse {
      0%, 100% { transform: scaleY(1); }
      50% { transform: scaleY(1.2); }
    }
    .heartbeat {
      animation: heartbeat 1.5s infinite;
    }
    @keyframes heartbeat {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.1); }
    }
    .electrode-placement {
      position: relative;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .electrode-placement:hover {
      transform: scale(1.1);
      filter: brightness(1.2);
    }
    .signal-trace {
      stroke-dasharray: 1000;
      stroke-dashoffset: 1000;
      animation: drawTrace 3s ease-in-out infinite;
    }
    @keyframes drawTrace {
      to { stroke-dashoffset: 0; }
    }
    .interactive-demo {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border: 2px dashed #0ea5e9;
      transition: all 0.3s ease;
    }
    .interactive-demo:hover {
      background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
      border-color: #0284c7;
    }
  </style>
</head>
<body class="bg-gray-50 font-sans">
  <!-- Navigation -->
  <nav class="bg-white shadow-lg sticky top-0 z-50">
    <div class="container mx-auto px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4 space-x-reverse">
          <div class="text-2xl heartbeat">💓</div>
          <div>
            <h1 class="text-xl font-bold text-gray-900">أساسيات تخطيط القلب الكهربائي</h1>
            <p class="text-sm text-gray-500">ECG Fundamentals Course</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-4 space-x-reverse">
          <a href="electrophysiological-courses.html" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">العودة للتخصص</a>
          <a href="courses-index.html" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">فهرس المواد</a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Course Progress -->
  <div class="bg-green-600 text-white py-4">
    <div class="container mx-auto px-6">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium">تقدم المحاضرة</span>
        <span class="text-sm" id="progress-text">1 / 4</span>
      </div>
      <div class="w-full bg-green-800 rounded-full h-2">
        <div class="progress-bar bg-white h-2 rounded-full" style="width: 25%" id="progress-bar"></div>
      </div>
    </div>
  </div>

  <!-- Module Navigation -->
  <div class="bg-white border-b shadow-sm sticky top-20 z-40">
    <div class="container mx-auto px-6 py-3">
      <div class="flex flex-wrap gap-2">
        <button class="module-btn px-4 py-2 text-sm rounded-lg bg-green-100 text-green-800 font-medium" data-module="1">
          1. فيزيولوجيا القلب الكهربائية
        </button>
        <button class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="2">
          2. أقطاب ECG وتقنيات التوصيل
        </button>
        <button class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="3">
          3. مكبرات الإشارات الحيوية
        </button>
        <button class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="4">
          4. تحليل إشارات ECG
        </button>
      </div>
    </div>
  </div>

  <!-- Lecture Slides -->
  <section class="py-8">
    <div class="container mx-auto px-6">
      
      <!-- Slide 1: Heart Electrophysiology -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content active" id="slide-1">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4 heartbeat">💓</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">فيزيولوجيا القلب الكهربائية</h2>
          <p class="text-lg text-gray-600">Cardiac Electrophysiology</p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">النظام الكهربائي للقلب</h3>
            
            <div class="space-y-4">
              <div class="interactive-demo p-4 rounded-lg cursor-pointer" onclick="highlightNode('sa')">
                <div class="flex items-center">
                  <span class="text-3xl ml-4">🔴</span>
                  <div>
                    <h4 class="font-semibold text-gray-900">العقدة الجيبية الأذينية (SA Node)</h4>
                    <p class="text-sm text-gray-600">منظم ضربات القلب الطبيعي - 60-100 نبضة/دقيقة</p>
                  </div>
                </div>
              </div>

              <div class="interactive-demo p-4 rounded-lg cursor-pointer" onclick="highlightNode('av')">
                <div class="flex items-center">
                  <span class="text-3xl ml-4">🟡</span>
                  <div>
                    <h4 class="font-semibold text-gray-900">العقدة الأذينية البطينية (AV Node)</h4>
                    <p class="text-sm text-gray-600">تأخير الإشارة - 0.1-0.2 ثانية</p>
                  </div>
                </div>
              </div>

              <div class="interactive-demo p-4 rounded-lg cursor-pointer" onclick="highlightNode('bundle')">
                <div class="flex items-center">
                  <span class="text-3xl ml-4">🔵</span>
                  <div>
                    <h4 class="font-semibold text-gray-900">حزمة هيس والألياف</h4>
                    <p class="text-sm text-gray-600">توصيل سريع للبطينين</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-6">
            <h4 class="font-semibold text-gray-900 mb-4">دورة القلب الكهربائية</h4>
            <div id="heart-diagram" class="text-center">
              <div class="relative w-48 h-48 mx-auto bg-white rounded-full shadow-lg flex items-center justify-center">
                <div class="text-6xl heartbeat">❤️</div>
                <div class="absolute top-4 right-8 w-3 h-3 bg-red-500 rounded-full electrode-placement" data-node="sa" title="SA Node"></div>
                <div class="absolute bottom-8 left-8 w-3 h-3 bg-yellow-500 rounded-full electrode-placement" data-node="av" title="AV Node"></div>
                <div class="absolute bottom-4 right-12 w-3 h-3 bg-blue-500 rounded-full electrode-placement" data-node="bundle" title="Bundle of His"></div>
              </div>
            </div>
            <p class="text-sm text-gray-600 mt-4 text-center">انقر على النقاط الملونة لاستكشاف النظام الكهربائي</p>
          </div>
        </div>

        <div class="mt-8 bg-blue-50 rounded-lg p-6">
          <h4 class="text-lg font-semibold text-gray-900 mb-4">جهد العمل القلبي</h4>
          <div class="grid md:grid-cols-3 gap-4">
            <div class="bg-white p-4 rounded-lg text-center">
              <div class="text-2xl mb-2">⚡</div>
              <div class="font-medium">المرحلة 0</div>
              <div class="text-sm text-gray-600">إزالة الاستقطاب السريع</div>
            </div>
            <div class="bg-white p-4 rounded-lg text-center">
              <div class="text-2xl mb-2">📈</div>
              <div class="font-medium">المرحلة 2</div>
              <div class="text-sm text-gray-600">الهضبة</div>
            </div>
            <div class="bg-white p-4 rounded-lg text-center">
              <div class="text-2xl mb-2">📉</div>
              <div class="font-medium">المرحلة 3</div>
              <div class="text-sm text-gray-600">إعادة الاستقطاب</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Slide 2: ECG Electrodes -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content" id="slide-2" style="display: none;">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4">🔌</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">أقطاب ECG وتقنيات التوصيل</h2>
          <p class="text-lg text-gray-600">ECG Electrodes and Lead Systems</p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">نظام الـ 12 قطب</h3>
            
            <div class="space-y-4">
              <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">الأقطاب الطرفية (Limb Leads)</h4>
                <div class="grid grid-cols-3 gap-2 text-sm">
                  <div class="bg-white p-2 rounded text-center">I</div>
                  <div class="bg-white p-2 rounded text-center">II</div>
                  <div class="bg-white p-2 rounded text-center">III</div>
                  <div class="bg-white p-2 rounded text-center">aVR</div>
                  <div class="bg-white p-2 rounded text-center">aVL</div>
                  <div class="bg-white p-2 rounded text-center">aVF</div>
                </div>
              </div>

              <div class="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">الأقطاب الصدرية (Precordial Leads)</h4>
                <div class="grid grid-cols-3 gap-2 text-sm">
                  <div class="bg-white p-2 rounded text-center">V1</div>
                  <div class="bg-white p-2 rounded text-center">V2</div>
                  <div class="bg-white p-2 rounded text-center">V3</div>
                  <div class="bg-white p-2 rounded text-center">V4</div>
                  <div class="bg-white p-2 rounded text-center">V5</div>
                  <div class="bg-white p-2 rounded text-center">V6</div>
                </div>
              </div>
            </div>

            <div class="mt-6">
              <button type="button" class="w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors" onclick="simulateECGPlacement()">
                🎯 محاكاة وضع الأقطاب
              </button>
            </div>
          </div>

          <div class="bg-gray-50 rounded-lg p-6">
            <h4 class="font-semibold text-gray-900 mb-4">مخطط وضع الأقطاب</h4>
            <div id="electrode-placement" class="text-center">
              <div class="relative w-64 h-80 mx-auto bg-gradient-to-b from-blue-100 to-blue-200 rounded-lg shadow-lg">
                <!-- Body outline -->
                <div class="absolute inset-4 border-2 border-gray-400 rounded-lg"></div>
                
                <!-- Electrode positions -->
                <div class="absolute top-8 left-4 w-4 h-4 bg-red-500 rounded-full electrode-placement" data-lead="RA" title="Right Arm"></div>
                <div class="absolute top-8 right-4 w-4 h-4 bg-yellow-500 rounded-full electrode-placement" data-lead="LA" title="Left Arm"></div>
                <div class="absolute bottom-8 left-4 w-4 h-4 bg-green-500 rounded-full electrode-placement" data-lead="RL" title="Right Leg"></div>
                <div class="absolute bottom-8 right-4 w-4 h-4 bg-blue-500 rounded-full electrode-placement" data-lead="LL" title="Left Leg"></div>
                
                <!-- Precordial leads -->
                <div class="absolute top-16 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-purple-500 rounded-full electrode-placement" data-lead="V1" title="V1"></div>
                <div class="absolute top-20 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-purple-500 rounded-full electrode-placement" data-lead="V2" title="V2"></div>
                <div class="absolute top-24 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-purple-500 rounded-full electrode-placement" data-lead="V3" title="V3"></div>
                <div class="absolute top-28 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-purple-500 rounded-full electrode-placement" data-lead="V4" title="V4"></div>
                <div class="absolute top-32 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-purple-500 rounded-full electrode-placement" data-lead="V5" title="V5"></div>
                <div class="absolute top-36 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-purple-500 rounded-full electrode-placement" data-lead="V6" title="V6"></div>
              </div>
            </div>
            <p class="text-sm text-gray-600 mt-4 text-center">انقر على "محاكاة وضع الأقطاب" لرؤية التوضيح التفاعلي</p>
          </div>
        </div>
      </div>

      <!-- Navigation Controls -->
      <div class="flex items-center justify-between mt-8">
        <button type="button" id="prev-btn" class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
          ← السابق
        </button>
        
        <div class="flex items-center space-x-2 space-x-reverse">
          <span class="w-3 h-3 bg-green-600 rounded-full slide-indicator active" data-slide="1"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="2"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="3"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="4"></span>
        </div>
        
        <button type="button" id="next-btn" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
          التالي →
        </button>
      </div>
    </div>
  </section>

  <!-- Tooltip -->
  <div id="tooltip" class="fixed bg-gray-900 text-white p-3 rounded-lg shadow-lg z-50 opacity-0 pointer-events-none transition-opacity duration-200">
    <div id="tooltip-content"></div>
  </div>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-8">
    <div class="container mx-auto px-6 text-center">
      <p>&copy; 2025 BioMed LMS. جميع الحقوق محفوظة.</p>
    </div>
  </footer>

  <script src="/assets/js/utils.js"></script>
  <script>
    let currentSlide = 1;
    const totalSlides = 4;

    // Slide navigation functions
    function showSlide(slideNumber) {
      document.querySelectorAll('.slide-content').forEach(slide => {
        slide.style.display = 'none';
        slide.classList.remove('active');
      });

      const currentSlideEl = document.getElementById(`slide-${slideNumber}`);
      if (currentSlideEl) {
        currentSlideEl.style.display = 'block';
        setTimeout(() => {
          currentSlideEl.classList.add('active');
        }, 50);
      }

      updateProgress(slideNumber);
      updateNavigation(slideNumber);
      updateModuleButtons(slideNumber);
    }

    function updateProgress(slideNumber) {
      const progress = (slideNumber / totalSlides) * 100;
      document.getElementById('progress-bar').style.width = `${progress}%`;
      document.getElementById('progress-text').textContent = `${slideNumber} / ${totalSlides}`;
    }

    function updateNavigation(slideNumber) {
      const prevBtn = document.getElementById('prev-btn');
      const nextBtn = document.getElementById('next-btn');

      prevBtn.disabled = slideNumber === 1;
      nextBtn.disabled = slideNumber === totalSlides;

      document.querySelectorAll('.slide-indicator').forEach((indicator, index) => {
        if (index + 1 === slideNumber) {
          indicator.classList.add('active', 'bg-green-600');
          indicator.classList.remove('bg-gray-300');
        } else {
          indicator.classList.remove('active', 'bg-green-600');
          indicator.classList.add('bg-gray-300');
        }
      });
    }

    function updateModuleButtons(slideNumber) {
      document.querySelectorAll('.module-btn').forEach((btn, index) => {
        if (index + 1 === slideNumber) {
          btn.classList.remove('bg-gray-100', 'text-gray-600');
          btn.classList.add('bg-green-100', 'text-green-800', 'font-medium');
        } else {
          btn.classList.remove('bg-green-100', 'text-green-800', 'font-medium');
          btn.classList.add('bg-gray-100', 'text-gray-600');
        }
      });
    }

    // Interactive functions
    function highlightNode(nodeType) {
      const nodes = {
        sa: { name: 'العقدة الجيبية الأذينية', rate: '60-100 نبضة/دقيقة', function: 'منظم ضربات القلب الطبيعي' },
        av: { name: 'العقدة الأذينية البطينية', delay: '0.1-0.2 ثانية', function: 'تأخير وتنظيم الإشارة' },
        bundle: { name: 'حزمة هيس والألياف', speed: 'سريع', function: 'توصيل للبطينين' }
      };

      if (nodes[nodeType]) {
        const node = nodes[nodeType];
        showTooltip(`${node.name}: ${node.function}`);
        
        // Highlight the corresponding electrode
        const electrode = document.querySelector(`[data-node="${nodeType}"]`);
        if (electrode) {
          electrode.style.transform = 'scale(1.5)';
          electrode.style.boxShadow = '0 0 15px rgba(59, 130, 246, 0.8)';
          setTimeout(() => {
            electrode.style.transform = 'scale(1)';
            electrode.style.boxShadow = 'none';
          }, 1000);
        }
      }
    }

    function simulateECGPlacement() {
      const electrodes = document.querySelectorAll('.electrode-placement');
      const button = event.target;
      
      button.disabled = true;
      button.textContent = '🔄 جاري المحاكاة...';
      
      electrodes.forEach((electrode, index) => {
        setTimeout(() => {
          electrode.style.transform = 'scale(1.3)';
          electrode.style.boxShadow = '0 0 10px rgba(34, 197, 94, 0.8)';
          
          setTimeout(() => {
            electrode.style.transform = 'scale(1)';
            electrode.style.boxShadow = 'none';
          }, 500);
        }, index * 300);
      });
      
      setTimeout(() => {
        button.disabled = false;
        button.textContent = '🎯 محاكاة وضع الأقطاب';
        showTooltip('تم وضع جميع الأقطاب بنجاح! الآن يمكن تسجيل إشارات ECG');
      }, electrodes.length * 300 + 500);
    }

    function showTooltip(message) {
      const tooltip = document.getElementById('tooltip');
      const content = document.getElementById('tooltip-content');
      
      content.textContent = message;
      tooltip.style.opacity = '1';
      
      if (event) {
        tooltip.style.left = event.pageX + 10 + 'px';
        tooltip.style.top = event.pageY - 40 + 'px';
      }
      
      setTimeout(() => {
        tooltip.style.opacity = '0';
      }, 3000);
    }

    // Event listeners
    document.getElementById('next-btn').addEventListener('click', () => {
      if (currentSlide < totalSlides) {
        currentSlide++;
        showSlide(currentSlide);
      }
    });

    document.getElementById('prev-btn').addEventListener('click', () => {
      if (currentSlide > 1) {
        currentSlide--;
        showSlide(currentSlide);
      }
    });

    document.querySelectorAll('.module-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const moduleNumber = parseInt(btn.dataset.module);
        currentSlide = moduleNumber;
        showSlide(currentSlide);
      });
    });

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      showSlide(1);
    });
  </script>
</body>
</html>
