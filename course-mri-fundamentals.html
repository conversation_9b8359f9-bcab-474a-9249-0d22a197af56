<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>أساسيات الرنين المغناطيسي - BioMed LMS</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="/assets/css/main.css">
  <style>
    .lecture-slide {
      min-height: 600px;
      transition: all 0.5s ease;
    }
    .slide-content {
      opacity: 0;
      transform: translateY(20px);
      transition: all 0.6s ease;
    }
    .slide-content.active {
      opacity: 1;
      transform: translateY(0);
    }
    .magnetic-field {
      animation: magneticPulse 3s infinite;
    }
    @keyframes magneticPulse {
      0%, 100% { transform: scale(1); opacity: 0.7; }
      50% { transform: scale(1.1); opacity: 1; }
    }
    .proton-spin {
      animation: spin 2s linear infinite;
    }
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
    .signal-wave {
      animation: wave 2s ease-in-out infinite;
    }
    @keyframes wave {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-10px); }
    }
    .interactive-icon {
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .interactive-icon:hover {
      transform: scale(1.2);
      filter: brightness(1.2);
    }
    .progress-bar {
      transition: width 0.3s ease;
    }
    .module-nav {
      position: sticky;
      top: 80px;
      z-index: 40;
    }
  </style>
</head>
<body class="bg-gray-50 font-sans">
  <!-- Navigation -->
  <nav class="bg-white shadow-lg sticky top-0 z-50">
    <div class="container mx-auto px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4 space-x-reverse">
          <div class="text-2xl">🧲</div>
          <div>
            <h1 class="text-xl font-bold text-gray-900">أساسيات الرنين المغناطيسي</h1>
            <p class="text-sm text-gray-500">MRI Fundamentals Course</p>
          </div>
        </div>

        <div class="flex items-center space-x-4 space-x-reverse">
          <a href="biomedical-imaging-courses.html" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">العودة للتخصص</a>
          <a href="courses-index.html" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">فهرس المواد</a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Course Progress -->
  <div class="bg-blue-600 text-white py-4">
    <div class="container mx-auto px-6">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium">تقدم المحاضرة</span>
        <span class="text-sm" id="progress-text">1 / 6</span>
      </div>
      <div class="w-full bg-blue-800 rounded-full h-2">
        <div class="progress-bar bg-white h-2 rounded-full" style="width: 16.67%" id="progress-bar"></div>
      </div>
    </div>
  </div>

  <!-- Module Navigation -->
  <div class="module-nav bg-white border-b shadow-sm">
    <div class="container mx-auto px-6 py-3">
      <div class="flex flex-wrap gap-2">
        <button class="module-btn px-4 py-2 text-sm rounded-lg bg-blue-100 text-blue-800 font-medium" data-module="1">
          1. مقدمة الرنين المغناطيسي
        </button>
        <button class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="2">
          2. الفيزياء الأساسية
        </button>
        <button class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="3">
          3. تسلسل النبضات
        </button>
        <button class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="4">
          4. إعادة بناء الصورة
        </button>
        <button class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="5">
          5. التباين والتطبيقات
        </button>
        <button class="module-btn px-4 py-2 text-sm rounded-lg bg-gray-100 text-gray-600" data-module="6">
          6. السلامة والاعتبارات
        </button>
      </div>
    </div>
  </div>

  <!-- Lecture Slides -->
  <section class="py-8">
    <div class="container mx-auto px-6">

      <!-- Slide 1: Introduction -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content active" id="slide-1">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4 magnetic-field">🧲</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">مقدمة في الرنين المغناطيسي النووي</h2>
          <p class="text-lg text-gray-600">Nuclear Magnetic Resonance Introduction</p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">ما هو الرنين المغناطيسي؟</h3>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-start">
                <span class="text-blue-600 ml-3">•</span>
                تقنية تصوير طبي غير جراحية
              </li>
              <li class="flex items-start">
                <span class="text-blue-600 ml-3">•</span>
                تستخدم المجالات المغناطيسية القوية
              </li>
              <li class="flex items-start">
                <span class="text-blue-600 ml-3">•</span>
                تعتمد على خصائص نوى الهيدروجين
              </li>
              <li class="flex items-start">
                <span class="text-blue-600 ml-3">•</span>
                توفر صور عالية الدقة للأنسجة الرخوة
              </li>
            </ul>
          </div>

          <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6">
            <h4 class="font-semibold text-gray-900 mb-4">المكونات الأساسية</h4>
            <div class="space-y-4">
              <div class="interactive-icon flex items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md" onclick="showTooltip('magnet')">
                <span class="text-2xl ml-3">🧲</span>
                <div>
                  <div class="font-medium">المغناطيس الرئيسي</div>
                  <div class="text-sm text-gray-600">1.5-3 Tesla</div>
                </div>
              </div>
              <div class="interactive-icon flex items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md" onclick="showTooltip('coils')">
                <span class="text-2xl ml-3">⚡</span>
                <div>
                  <div class="font-medium">ملفات التدرج</div>
                  <div class="text-sm text-gray-600">Gradient Coils</div>
                </div>
              </div>
              <div class="interactive-icon flex items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md" onclick="showTooltip('rf')">
                <span class="text-2xl ml-3">📡</span>
                <div>
                  <div class="font-medium">ملفات الترددات اللاسلكية</div>
                  <div class="text-sm text-gray-600">RF Coils</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-8 text-center">
          <div class="inline-flex items-center space-x-4 space-x-reverse">
            <div class="interactive-icon text-4xl proton-spin" onclick="animateProton()">⚛️</div>
            <div class="text-sm text-gray-600">انقر على الذرة لرؤية دوران البروتون</div>
          </div>
        </div>
      </div>

      <!-- Slide 2: Basic Physics -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content" id="slide-2" style="display: none;">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4">⚛️</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">الفيزياء الأساسية للرنين المغناطيسي</h2>
          <p class="text-lg text-gray-600">Basic Physics of MRI</p>
        </div>

        <div class="grid md:grid-cols-3 gap-6">
          <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">1. اللف المغزلي للبروتون</h3>
            <div class="text-center mb-4">
              <div class="interactive-icon text-5xl proton-spin" onclick="toggleSpin()">🌀</div>
            </div>
            <ul class="text-sm text-gray-700 space-y-2">
              <li>• البروتونات لها لف مغزلي</li>
              <li>• تتصرف كمغناطيسات صغيرة</li>
              <li>• تتجه عشوائياً في الحالة الطبيعية</li>
            </ul>
          </div>

          <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">2. المجال المغناطيسي الخارجي</h3>
            <div class="text-center mb-4">
              <div class="interactive-icon text-5xl magnetic-field" onclick="alignProtons()">🧲</div>
            </div>
            <ul class="text-sm text-gray-700 space-y-2">
              <li>• يحاذي البروتونات مع المجال</li>
              <li>• ينتج مغنطة صافية</li>
              <li>• قوة المجال تحدد تردد الرنين</li>
            </ul>
          </div>

          <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">3. الإثارة بالترددات اللاسلكية</h3>
            <div class="text-center mb-4">
              <div class="interactive-icon text-5xl signal-wave" onclick="exciteProtons()">📡</div>
            </div>
            <ul class="text-sm text-gray-700 space-y-2">
              <li>• نبضة RF تثير البروتونات</li>
              <li>• تغير اتجاه المغنطة</li>
              <li>• تردد لارمور المحدد</li>
            </ul>
          </div>
        </div>

        <div class="mt-8 bg-gray-50 rounded-lg p-6">
          <h4 class="text-lg font-semibold text-gray-900 mb-4">معادلة لارمور</h4>
          <div class="text-center">
            <div class="text-2xl font-mono bg-white p-4 rounded-lg inline-block">
              f = γ × B₀
            </div>
            <div class="mt-4 text-sm text-gray-600">
              <p>حيث: f = تردد الرنين، γ = النسبة الجيرومغناطيسية، B₀ = قوة المجال المغناطيسي</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Slide 3: Pulse Sequences -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content" id="slide-3" style="display: none;">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4">⚡</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">تسلسل النبضات</h2>
          <p class="text-lg text-gray-600">Pulse Sequences</p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">أنواع تسلسل النبضات</h3>

            <div class="space-y-4">
              <div class="interactive-icon bg-gradient-to-r from-red-50 to-red-100 p-4 rounded-lg cursor-pointer hover:shadow-md" onclick="showSequence('se')">
                <div class="flex items-center">
                  <span class="text-3xl ml-4">🔄</span>
                  <div>
                    <h4 class="font-semibold text-gray-900">Spin Echo (SE)</h4>
                    <p class="text-sm text-gray-600">تسلسل الصدى المغزلي</p>
                  </div>
                </div>
              </div>

              <div class="interactive-icon bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg cursor-pointer hover:shadow-md" onclick="showSequence('gre')">
                <div class="flex items-center">
                  <span class="text-3xl ml-4">⚡</span>
                  <div>
                    <h4 class="font-semibold text-gray-900">Gradient Echo (GRE)</h4>
                    <p class="text-sm text-gray-600">تسلسل صدى التدرج</p>
                  </div>
                </div>
              </div>

              <div class="interactive-icon bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg cursor-pointer hover:shadow-md" onclick="showSequence('flair')">
                <div class="flex items-center">
                  <span class="text-3xl ml-4">💫</span>
                  <div>
                    <h4 class="font-semibold text-gray-900">FLAIR</h4>
                    <p class="text-sm text-gray-600">استرداد الانعكاس المثبط للسوائل</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 rounded-lg p-6">
            <h4 class="font-semibold text-gray-900 mb-4">مخطط التسلسل</h4>
            <div id="sequence-diagram" class="text-center">
              <div class="bg-white p-8 rounded-lg">
                <div class="text-4xl mb-4">📊</div>
                <p class="text-gray-600">انقر على نوع التسلسل لعرض المخطط</p>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-8 bg-blue-50 rounded-lg p-6">
          <h4 class="text-lg font-semibold text-gray-900 mb-4">المعاملات الأساسية</h4>
          <div class="grid md:grid-cols-3 gap-4">
            <div class="bg-white p-4 rounded-lg text-center">
              <div class="text-2xl mb-2">⏱️</div>
              <div class="font-medium">TR (Time of Repetition)</div>
              <div class="text-sm text-gray-600">زمن التكرار</div>
            </div>
            <div class="bg-white p-4 rounded-lg text-center">
              <div class="text-2xl mb-2">🔄</div>
              <div class="font-medium">TE (Time of Echo)</div>
              <div class="text-sm text-gray-600">زمن الصدى</div>
            </div>
            <div class="bg-white p-4 rounded-lg text-center">
              <div class="text-2xl mb-2">🎯</div>
              <div class="font-medium">Flip Angle</div>
              <div class="text-sm text-gray-600">زاوية الانحراف</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Slide 4: Image Reconstruction -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content" id="slide-4" style="display: none;">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4">🖼️</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">إعادة بناء الصورة</h2>
          <p class="text-lg text-gray-600">Image Reconstruction</p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">خطوات إعادة البناء</h3>

            <div class="space-y-4">
              <div class="flex items-center p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg">
                <div class="text-3xl ml-4">1️⃣</div>
                <div>
                  <h4 class="font-semibold">جمع البيانات</h4>
                  <p class="text-sm text-gray-600">K-space data acquisition</p>
                </div>
              </div>

              <div class="flex items-center p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg">
                <div class="text-3xl ml-4">2️⃣</div>
                <div>
                  <h4 class="font-semibold">تحويل فورييه</h4>
                  <p class="text-sm text-gray-600">Fourier Transform</p>
                </div>
              </div>

              <div class="flex items-center p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg">
                <div class="text-3xl ml-4">3️⃣</div>
                <div>
                  <h4 class="font-semibold">معالجة الصورة</h4>
                  <p class="text-sm text-gray-600">Image processing</p>
                </div>
              </div>
            </div>

            <div class="mt-6">
              <button class="interactive-icon w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors" onclick="simulateReconstruction()">
                🎬 محاكاة إعادة البناء
              </button>
            </div>
          </div>

          <div class="bg-gray-50 rounded-lg p-6">
            <h4 class="font-semibold text-gray-900 mb-4">K-Space</h4>
            <div id="kspace-demo" class="text-center">
              <div class="bg-white p-6 rounded-lg">
                <div class="grid grid-cols-8 gap-1 mb-4">
                  <!-- K-space grid -->
                  <div class="w-6 h-6 bg-gray-200 rounded kspace-cell"></div>
                  <div class="w-6 h-6 bg-gray-200 rounded kspace-cell"></div>
                  <div class="w-6 h-6 bg-gray-200 rounded kspace-cell"></div>
                  <div class="w-6 h-6 bg-gray-200 rounded kspace-cell"></div>
                  <div class="w-6 h-6 bg-gray-200 rounded kspace-cell"></div>
                  <div class="w-6 h-6 bg-gray-200 rounded kspace-cell"></div>
                  <div class="w-6 h-6 bg-gray-200 rounded kspace-cell"></div>
                  <div class="w-6 h-6 bg-gray-200 rounded kspace-cell"></div>
                </div>
                <p class="text-sm text-gray-600">انقر على "محاكاة إعادة البناء" لرؤية العملية</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Slide 5: Contrast and Applications -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content" id="slide-5" style="display: none;">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4">🎨</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">التباين والتطبيقات السريرية</h2>
          <p class="text-lg text-gray-600">Contrast and Clinical Applications</p>
        </div>

        <div class="grid md:grid-cols-3 gap-6">
          <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">T1-Weighted</h3>
            <div class="text-center mb-4">
              <div class="interactive-icon text-4xl" onclick="showContrastExample('t1')">🧠</div>
            </div>
            <ul class="text-sm text-gray-700 space-y-2">
              <li>• الدهون: إشارة عالية (أبيض)</li>
              <li>• الماء: إشارة منخفضة (أسود)</li>
              <li>• مفيد لتشريح الدماغ</li>
            </ul>
          </div>

          <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">T2-Weighted</h3>
            <div class="text-center mb-4">
              <div class="interactive-icon text-4xl" onclick="showContrastExample('t2')">💧</div>
            </div>
            <ul class="text-sm text-gray-700 space-y-2">
              <li>• الماء: إشارة عالية (أبيض)</li>
              <li>• الدهون: إشارة متوسطة</li>
              <li>• مفيد لكشف الوذمة</li>
            </ul>
          </div>

          <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">FLAIR</h3>
            <div class="text-center mb-4">
              <div class="interactive-icon text-4xl" onclick="showContrastExample('flair')">✨</div>
            </div>
            <ul class="text-sm text-gray-700 space-y-2">
              <li>• يثبط إشارة السوائل</li>
              <li>• يبرز الآفات حول البطينات</li>
              <li>• مفيد في أمراض المادة البيضاء</li>
            </ul>
          </div>
        </div>

        <div class="mt-8 bg-gray-50 rounded-lg p-6">
          <h4 class="text-lg font-semibold text-gray-900 mb-4">التطبيقات السريرية</h4>
          <div class="grid md:grid-cols-2 gap-6">
            <div>
              <h5 class="font-medium text-gray-900 mb-3">تصوير الدماغ</h5>
              <ul class="text-sm text-gray-700 space-y-1">
                <li>• السكتة الدماغية</li>
                <li>• الأورام</li>
                <li>• التصلب المتعدد</li>
                <li>• الصرع</li>
              </ul>
            </div>
            <div>
              <h5 class="font-medium text-gray-900 mb-3">تصوير العضلات والعظام</h5>
              <ul class="text-sm text-gray-700 space-y-1">
                <li>• إصابات الأربطة</li>
                <li>• تمزق الغضاريف</li>
                <li>• التهاب المفاصل</li>
                <li>• أورام العظام</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Slide 6: Safety Considerations -->
      <div class="lecture-slide bg-white rounded-xl shadow-lg p-8 mb-8 slide-content" id="slide-6" style="display: none;">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4">🛡️</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">السلامة والاعتبارات</h2>
          <p class="text-lg text-gray-600">Safety and Considerations</p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">مخاطر السلامة</h3>

            <div class="space-y-4">
              <div class="bg-red-50 border-r-4 border-red-500 p-4 rounded-lg">
                <div class="flex items-center">
                  <span class="text-2xl ml-3">⚠️</span>
                  <div>
                    <h4 class="font-semibold text-red-800">الأجسام المعدنية</h4>
                    <p class="text-sm text-red-700">يمكن أن تصبح مقذوفات خطيرة</p>
                  </div>
                </div>
              </div>

              <div class="bg-yellow-50 border-r-4 border-yellow-500 p-4 rounded-lg">
                <div class="flex items-center">
                  <span class="text-2xl ml-3">🔥</span>
                  <div>
                    <h4 class="font-semibold text-yellow-800">التسخين بالترددات اللاسلكية</h4>
                    <p class="text-sm text-yellow-700">SAR (معدل الامتصاص النوعي)</p>
                  </div>
                </div>
              </div>

              <div class="bg-blue-50 border-r-4 border-blue-500 p-4 rounded-lg">
                <div class="flex items-center">
                  <span class="text-2xl ml-3">🔊</span>
                  <div>
                    <h4 class="font-semibold text-blue-800">الضوضاء الصوتية</h4>
                    <p class="text-sm text-blue-700">يمكن أن تصل إلى 120 ديسيبل</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">موانع الاستعمال</h3>

            <div class="space-y-4">
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">موانع مطلقة</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• منظم ضربات القلب القديم</li>
                  <li>• مقاطع الأوعية الدموية المعدنية</li>
                  <li>• شظايا معدنية في العين</li>
                  <li>• مضخات الأنسولين القديمة</li>
                </ul>
              </div>

              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-2">موانع نسبية</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                  <li>• الحمل (الثلث الأول)</li>
                  <li>• رهاب الأماكن المغلقة</li>
                  <li>• عدم القدرة على البقاء ساكناً</li>
                  <li>• الوشم الحديث</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-8 bg-green-50 rounded-lg p-6">
          <h4 class="text-lg font-semibold text-gray-900 mb-4">إرشادات السلامة</h4>
          <div class="grid md:grid-cols-3 gap-4">
            <div class="text-center">
              <div class="text-3xl mb-2">🔍</div>
              <div class="font-medium">فحص شامل</div>
              <div class="text-sm text-gray-600">قبل دخول غرفة MRI</div>
            </div>
            <div class="text-center">
              <div class="text-3xl mb-2">👂</div>
              <div class="font-medium">حماية السمع</div>
              <div class="text-sm text-gray-600">سدادات الأذن الإجبارية</div>
            </div>
            <div class="text-center">
              <div class="text-3xl mb-2">📋</div>
              <div class="font-medium">استمارة السلامة</div>
              <div class="text-sm text-gray-600">يجب ملؤها بدقة</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation Controls -->
      <div class="flex items-center justify-between mt-8">
        <button id="prev-btn" class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
          ← السابق
        </button>

        <div class="flex items-center space-x-2 space-x-reverse">
          <span class="w-3 h-3 bg-blue-600 rounded-full slide-indicator active" data-slide="1"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="2"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="3"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="4"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="5"></span>
          <span class="w-3 h-3 bg-gray-300 rounded-full slide-indicator" data-slide="6"></span>
        </div>

        <button id="next-btn" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          التالي →
        </button>
      </div>
    </div>
  </section>

  <!-- Tooltip -->
  <div id="tooltip" class="fixed bg-gray-900 text-white p-3 rounded-lg shadow-lg z-50 opacity-0 pointer-events-none transition-opacity duration-200">
    <div id="tooltip-content"></div>
  </div>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-8">
    <div class="container mx-auto px-6 text-center">
      <p>&copy; 2025 BioMed LMS. جميع الحقوق محفوظة.</p>
    </div>
  </footer>

  <script src="/assets/js/utils.js"></script>
  <script>
    let currentSlide = 1;
    const totalSlides = 6;

    // Slide navigation
    function showSlide(slideNumber) {
      // Hide all slides
      document.querySelectorAll('.slide-content').forEach(slide => {
        slide.style.display = 'none';
        slide.classList.remove('active');
      });

      // Show current slide
      const currentSlideEl = document.getElementById(`slide-${slideNumber}`);
      if (currentSlideEl) {
        currentSlideEl.style.display = 'block';
        setTimeout(() => {
          currentSlideEl.classList.add('active');
        }, 50);
      }

      // Update progress
      updateProgress(slideNumber);
      updateNavigation(slideNumber);
      updateModuleButtons(slideNumber);
    }

    function updateProgress(slideNumber) {
      const progress = (slideNumber / totalSlides) * 100;
      document.getElementById('progress-bar').style.width = `${progress}%`;
      document.getElementById('progress-text').textContent = `${slideNumber} / ${totalSlides}`;
    }

    function updateNavigation(slideNumber) {
      const prevBtn = document.getElementById('prev-btn');
      const nextBtn = document.getElementById('next-btn');

      prevBtn.disabled = slideNumber === 1;
      nextBtn.disabled = slideNumber === totalSlides;

      // Update indicators
      document.querySelectorAll('.slide-indicator').forEach((indicator, index) => {
        if (index + 1 === slideNumber) {
          indicator.classList.add('active');
          indicator.classList.remove('bg-gray-300');
          indicator.classList.add('bg-blue-600');
        } else {
          indicator.classList.remove('active');
          indicator.classList.remove('bg-blue-600');
          indicator.classList.add('bg-gray-300');
        }
      });
    }

    function updateModuleButtons(slideNumber) {
      document.querySelectorAll('.module-btn').forEach((btn, index) => {
        if (index + 1 === slideNumber) {
          btn.classList.remove('bg-gray-100', 'text-gray-600');
          btn.classList.add('bg-blue-100', 'text-blue-800', 'font-medium');
        } else {
          btn.classList.remove('bg-blue-100', 'text-blue-800', 'font-medium');
          btn.classList.add('bg-gray-100', 'text-gray-600');
        }
      });
    }

    // Event listeners
    document.getElementById('next-btn').addEventListener('click', () => {
      if (currentSlide < totalSlides) {
        currentSlide++;
        showSlide(currentSlide);
      }
    });

    document.getElementById('prev-btn').addEventListener('click', () => {
      if (currentSlide > 1) {
        currentSlide--;
        showSlide(currentSlide);
      }
    });

    // Module navigation
    document.querySelectorAll('.module-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const moduleNumber = parseInt(btn.dataset.module);
        currentSlide = moduleNumber;
        showSlide(currentSlide);
      });
    });

    // Interactive animations
    function animateProton() {
      const proton = event.target;
      proton.style.animation = 'none';
      setTimeout(() => {
        proton.style.animation = 'spin 0.5s linear 3';
      }, 10);

      // Show educational message
      showTooltip('proton');
    }

    function toggleSpin() {
      const element = event.target;
      element.classList.toggle('proton-spin');

      // Visual feedback
      element.style.transform = 'scale(1.1)';
      setTimeout(() => {
        element.style.transform = 'scale(1)';
      }, 200);
    }

    function alignProtons() {
      const element = event.target;
      element.style.transform = 'scale(1.2)';
      element.style.filter = 'brightness(1.2)';
      setTimeout(() => {
        element.style.transform = 'scale(1)';
        element.style.filter = 'brightness(1)';
      }, 300);
    }

    function exciteProtons() {
      const element = event.target;
      element.style.animation = 'wave 0.5s ease-in-out 3';

      // Add glow effect
      element.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.5)';
      setTimeout(() => {
        element.style.boxShadow = 'none';
      }, 1500);
    }

    // Pulse sequence demonstrations
    function showSequence(type) {
      const diagram = document.getElementById('sequence-diagram');

      const sequences = {
        se: {
          title: 'Spin Echo Sequence',
          content: `
            <div class="bg-white p-6 rounded-lg">
              <h5 class="font-semibold mb-4">تسلسل الصدى المغزلي</h5>
              <div class="space-y-2">
                <div class="flex items-center">
                  <div class="w-16 h-4 bg-red-500 rounded mr-3"></div>
                  <span class="text-sm">90° RF Pulse</span>
                </div>
                <div class="flex items-center">
                  <div class="w-8 h-4 bg-blue-500 rounded mr-3 ml-8"></div>
                  <span class="text-sm">180° RF Pulse</span>
                </div>
                <div class="flex items-center">
                  <div class="w-12 h-4 bg-green-500 rounded mr-3 ml-12"></div>
                  <span class="text-sm">Echo Signal</span>
                </div>
              </div>
              <p class="text-xs text-gray-600 mt-4">TR: 500-2000ms, TE: 20-100ms</p>
            </div>
          `
        },
        gre: {
          title: 'Gradient Echo Sequence',
          content: `
            <div class="bg-white p-6 rounded-lg">
              <h5 class="font-semibold mb-4">تسلسل صدى التدرج</h5>
              <div class="space-y-2">
                <div class="flex items-center">
                  <div class="w-12 h-4 bg-purple-500 rounded mr-3"></div>
                  <span class="text-sm">RF Pulse (< 90°)</span>
                </div>
                <div class="flex items-center">
                  <div class="w-16 h-4 bg-yellow-500 rounded mr-3"></div>
                  <span class="text-sm">Gradient Echo</span>
                </div>
              </div>
              <p class="text-xs text-gray-600 mt-4">TR: 20-100ms, TE: 5-15ms</p>
            </div>
          `
        },
        flair: {
          title: 'FLAIR Sequence',
          content: `
            <div class="bg-white p-6 rounded-lg">
              <h5 class="font-semibold mb-4">تسلسل FLAIR</h5>
              <div class="space-y-2">
                <div class="flex items-center">
                  <div class="w-20 h-4 bg-indigo-500 rounded mr-3"></div>
                  <span class="text-sm">180° Inversion</span>
                </div>
                <div class="flex items-center">
                  <div class="w-16 h-4 bg-red-500 rounded mr-3 ml-4"></div>
                  <span class="text-sm">90° RF Pulse</span>
                </div>
                <div class="flex items-center">
                  <div class="w-8 h-4 bg-blue-500 rounded mr-3 ml-8"></div>
                  <span class="text-sm">180° RF Pulse</span>
                </div>
              </div>
              <p class="text-xs text-gray-600 mt-4">TI: 2000-2500ms (لإلغاء إشارة السوائل)</p>
            </div>
          `
        }
      };

      if (sequences[type]) {
        diagram.innerHTML = sequences[type].content;

        // Add animation effect
        diagram.style.opacity = '0';
        setTimeout(() => {
          diagram.style.opacity = '1';
        }, 100);
      }
    }

    // K-space reconstruction simulation
    function simulateReconstruction() {
      const cells = document.querySelectorAll('.kspace-cell');
      const button = event.target;

      button.disabled = true;
      button.textContent = '🔄 جاري المحاكاة...';

      // Animate K-space filling
      cells.forEach((cell, index) => {
        setTimeout(() => {
          cell.style.backgroundColor = '#3b82f6';
          cell.style.transform = 'scale(1.1)';
          setTimeout(() => {
            cell.style.transform = 'scale(1)';
          }, 100);
        }, index * 200);
      });

      // Show reconstruction result
      setTimeout(() => {
        const demo = document.getElementById('kspace-demo');
        demo.innerHTML = `
          <div class="bg-white p-6 rounded-lg">
            <div class="text-4xl mb-4">🖼️</div>
            <h5 class="font-semibold mb-2">الصورة المعاد بناؤها</h5>
            <div class="w-32 h-32 bg-gradient-to-br from-gray-300 to-gray-500 rounded-lg mx-auto mb-4 relative">
              <div class="absolute inset-4 bg-white rounded opacity-80"></div>
              <div class="absolute inset-8 bg-gray-600 rounded"></div>
            </div>
            <p class="text-sm text-gray-600">تم تحويل بيانات K-space إلى صورة MRI</p>
          </div>
        `;

        button.disabled = false;
        button.textContent = '🎬 محاكاة إعادة البناء';
      }, 2000);
    }

    // Contrast examples
    function showContrastExample(type) {
      const examples = {
        t1: 'T1: الدهون ساطعة، الماء داكن - مثالي لتشريح الدماغ',
        t2: 'T2: الماء ساطع، الدهون متوسطة - مثالي لكشف الوذمة والالتهاب',
        flair: 'FLAIR: يثبط السوائل - مثالي لآفات المادة البيضاء'
      };

      if (examples[type]) {
        showTooltip('contrast', examples[type]);
      }
    }

    // Enhanced tooltip functionality
    function showTooltip(type, customMessage = null) {
      const tooltip = document.getElementById('tooltip');
      const content = document.getElementById('tooltip-content');

      const tooltips = {
        magnet: 'المغناطيس الرئيسي: ينتج مجالاً مغناطيسياً قوياً ومنتظماً (1.5-3 Tesla)',
        coils: 'ملفات التدرج: تنتج تدرجات مغناطيسية للترميز المكاني',
        rf: 'ملفات RF: ترسل وتستقبل إشارات الترددات اللاسلكية',
        proton: 'البروتونات تدور حول محورها وتتصرف كمغناطيسات صغيرة',
        contrast: customMessage || 'أنواع التباين المختلفة تبرز أنسجة مختلفة'
      };

      const message = customMessage || tooltips[type];
      if (message) {
        content.textContent = message;
        tooltip.style.opacity = '1';

        // Position tooltip near cursor
        if (event) {
          tooltip.style.left = event.pageX + 10 + 'px';
          tooltip.style.top = event.pageY - 40 + 'px';
        }

        setTimeout(() => {
          tooltip.style.opacity = '0';
        }, 3000);
      }
    }

    // Quiz functionality
    function startQuiz() {
      const quizQuestions = [
        {
          question: "ما هو تردد الرنين للبروتون في مجال مغناطيسي 1.5 Tesla؟",
          options: ["42.6 MHz", "63.9 MHz", "85.2 MHz", "127.8 MHz"],
          correct: 1,
          explanation: "تردد الرنين = γ × B₀ = 42.6 MHz/T × 1.5T = 63.9 MHz"
        },
        {
          question: "أي تسلسل نبضات يوفر أفضل تباين T2؟",
          options: ["Gradient Echo", "Spin Echo", "FLAIR", "T1-weighted"],
          correct: 1,
          explanation: "تسلسل Spin Echo يوفر أفضل تباين T2 لأنه يلغي تأثيرات عدم التجانس"
        }
      ];

      // Implementation would show quiz modal
      alert("سيتم إضافة الاختبار التفاعلي قريباً!");
    }

    // Progress tracking
    function updateLearningProgress() {
      const progress = {
        currentSlide: currentSlide,
        totalSlides: totalSlides,
        timeSpent: Date.now() - startTime,
        interactionsCount: interactionCount
      };

      localStorage.setItem('mri-course-progress', JSON.stringify(progress));
    }

    // Initialize tracking variables
    let startTime = Date.now();
    let interactionCount = 0;

    // Track interactions
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('interactive-icon')) {
        interactionCount++;
        updateLearningProgress();
      }
    });

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      showSlide(1);
    });
  </script>
</body>
</html>
