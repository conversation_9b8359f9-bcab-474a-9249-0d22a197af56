
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BioMed LMS - Biomedical Learning Management System</title>
  <link rel="icon" type="image/svg+xml" href="/public/favicon.svg">
  <meta name="description" content="Comprehensive biomedical learning management system for medical students and professionals. Discover, track, and excel in your medical learning journey.">
  <meta name="keywords" content="biomedical, LMS, medical education, learning management system, medical students, healthcare education">
  <meta name="author" content="BioMed LMS Team">
  <meta name="theme-color" content="#2563eb">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="BioMed LMS - Biomedical Learning Management System">
  <meta property="og:description" content="Comprehensive biomedical learning management system for medical students and professionals">
  <meta property="og:url" content="https://biomed-lms.com">
  <meta property="og:image" content="/assets/images/og-image.jpg">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:title" content="BioMed LMS - Biomedical Learning Management System">
  <meta property="twitter:description" content="Comprehensive biomedical learning management system for medical students and professionals">
  <meta property="twitter:image" content="/assets/images/twitter-card.jpg">

  <!-- Preload critical resources -->
  <link rel="preload" href="/assets/css/main.css" as="style">
  <link rel="preload" href="/assets/js/utils.js" as="script">

  <!-- CSS -->
  <link rel="stylesheet" href="/assets/css/main.css">
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {"50":"#eff6ff","100":"#dbeafe","200":"#bfdbfe","300":"#93c5fd","400":"#60a5fa","500":"#3b82f6","600":"#2563eb","700":"#1d4ed8","800":"#1e40af","900":"#1e3a8a","950":"#172554"},
            secondary: {"50":"#f0f9ff","100":"#e0f2fe","200":"#bae6fd","300":"#7dd3fc","400":"#38bdf8","500":"#0ea5e9","600":"#0284c7","700":"#0369a1","800":"#075985","900":"#0c4a6e","950":"#082f49"},
            accent: {"red":"#ef4444","orange":"#f97316","yellow":"#eab308","green":"#22c55e","purple":"#8b5cf6"}
          },
          animation: {
            'fade-in': 'fadeIn 0.6s ease-out',
            'slide-up': 'slideUp 0.6s ease-out',
            'bounce-gentle': 'bounceGentle 2s infinite',
            'pulse-slow': 'pulse 3s infinite'
          },
          keyframes: {
            fadeIn: {
              '0%': { opacity: '0', transform: 'translateY(20px)' },
              '100%': { opacity: '1', transform: 'translateY(0)' }
            },
            slideUp: {
              '0%': { opacity: '0', transform: 'translateY(30px)' },
              '100%': { opacity: '1', transform: 'translateY(0)' }
            },
            bounceGentle: {
              '0%, 100%': { transform: 'translateY(0)' },
              '50%': { transform: 'translateY(-10px)' }
            }
          }
        }
      }
    }
  </script>

  <!-- React Import Map -->
  <script type="importmap">
  {
    "imports": {
      "react": "https://esm.sh/react@18.3.1",
      "react-dom": "https://esm.sh/react-dom@18.3.1",
      "react-dom/client": "https://esm.sh/react-dom@18.3.1/client",
      "react-router-dom": "https://esm.sh/react-router-dom@6.23.1",
      "@google/genai": "https://esm.sh/@google/genai@0.12.0",
      "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
      "react/": "https://esm.sh/react@^19.1.0/"
    }
  }
  </script>

  <style>
    /* Loading screen styles */
    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #eff6ff 0%, #ffffff 50%, #f0fdf4 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.5s ease-out;
    }

    .loading-screen.hidden {
      opacity: 0;
      pointer-events: none;
    }

    /* Navigation enhancement */
    .nav-indicator {
      position: absolute;
      bottom: 0;
      left: 0;
      height: 2px;
      background: linear-gradient(90deg, #3b82f6, #22c55e);
      transition: all 0.3s ease;
    }

    /* Smooth scroll behavior */
    html {
      scroll-behavior: smooth;
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
    }

    ::-webkit-scrollbar-track {
      background: #f1f5f9;
    }

    ::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: #94a3b8;
    }
  </style>
</head>
<body class="bg-gray-50 font-sans antialiased">
  <!-- Loading Screen -->
  <div id="loading-screen" class="loading-screen">
    <div class="text-center">
      <div class="text-6xl mb-4 animate-bounce-gentle">🏥</div>
      <div class="text-xl font-semibold text-gray-700 mb-2">BioMed LMS</div>
      <div class="text-sm text-gray-500">Loading your medical education platform...</div>
      <div class="mt-4 w-32 h-1 bg-gray-200 rounded-full overflow-hidden">
        <div class="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full animate-pulse"></div>
      </div>
    </div>
  </div>

  <!-- Navigation Bar -->
  <nav id="main-nav" class="fixed top-0 left-0 right-0 bg-white/95 backdrop-blur-sm shadow-sm z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="text-2xl">🏥</div>
          <div>
            <h1 class="text-xl font-bold text-gray-900">BioMed LMS</h1>
            <p class="text-xs text-gray-500">Medical Education Platform</p>
          </div>
        </div>

        <div class="hidden md:flex items-center space-x-6">
          <a href="#react-app" class="nav-link text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors">React App</a>
          <a href="navigation-index.html" class="nav-link text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors">Navigation Hub</a>
          <a href="courses-index.html" class="nav-link text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors">Course Index</a>
          <a href="subject-categories-preview.html" class="nav-link text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors">Subjects</a>
          <a href="enhanced-course-details-preview.html" class="nav-link text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors">Courses</a>
          <a href="sitemap.html" class="nav-link text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors">Site Map</a>
        </div>

        <!-- Mobile menu button -->
        <button id="mobile-menu-btn" class="md:hidden p-2 rounded-md text-gray-700 hover:bg-gray-100">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>

      <!-- Mobile menu -->
      <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4 border-t border-gray-200">
        <div class="flex flex-col space-y-2 pt-4">
          <a href="#react-app" class="block px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md">React App</a>
          <a href="navigation-index.html" class="block px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md">Navigation Hub</a>
          <a href="enhanced-homepage-preview.html" class="block px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md">Enhanced Home</a>
          <a href="subject-categories-preview.html" class="block px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md">Subjects</a>
          <a href="enhanced-course-details-preview.html" class="block px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md">Courses</a>
          <a href="sitemap.html" class="block px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md">Site Map</a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <main class="pt-20">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-50 via-white to-green-50 py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
      <!-- Background decoration -->
      <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div class="absolute top-10 right-10 w-32 h-32 bg-blue-200 rounded-full opacity-20 animate-pulse-slow"></div>
      <div class="absolute bottom-10 left-10 w-24 h-24 bg-green-200 rounded-full opacity-20 animate-pulse-slow" style="animation-delay: 1s;"></div>

      <div class="relative max-w-7xl mx-auto text-center">
        <div class="animate-fade-in">
          <h1 class="text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight">
            Welcome to
            <span class="bg-gradient-to-r from-blue-600 via-purple-600 to-green-600 bg-clip-text text-transparent">
              BioMed LMS
            </span>
          </h1>
          <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
            Your comprehensive platform for biomedical education. Discover cutting-edge learning management systems,
            track your progress, and excel in your medical learning journey with our advanced educational tools.
          </p>
        </div>

        <div class="animate-slide-up flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
          <a href="#react-app" class="group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
            <span class="flex items-center">
              Launch React App
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
              </svg>
            </span>
          </a>
          <a href="navigation-index.html" class="group bg-white hover:bg-gray-50 text-blue-600 border-2 border-blue-600 hover:border-blue-700 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
            <span class="flex items-center">
              Explore All Pages
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </span>
          </a>
        </div>

        <!-- Feature Cards -->
        <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <div class="group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
            <div class="text-4xl mb-4 group-hover:scale-110 transition-transform">⚛️</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">React Application</h3>
            <p class="text-gray-600 mb-4">Interactive TypeScript-based application with modern routing and state management</p>
            <a href="#react-app" class="text-blue-600 hover:text-blue-700 font-medium text-sm">Launch App →</a>
          </div>

          <div class="group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
            <div class="text-4xl mb-4 group-hover:scale-110 transition-transform">🔬</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Biomedical Imaging</h3>
            <p class="text-gray-600 mb-4">Advanced imaging technologies: MRI, CT, Ultrasound, Nuclear Medicine</p>
            <a href="biomedical-imaging-courses.html" class="text-blue-600 hover:text-blue-700 font-medium text-sm">View Courses →</a>
          </div>

          <div class="group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
            <div class="text-4xl mb-4 group-hover:scale-110 transition-transform">⚡</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Electrophysiological Systems</h3>
            <p class="text-gray-600 mb-4">ECG, EEG, EMG systems and bioamplifiers for signal analysis</p>
            <a href="electrophysiological-courses.html" class="text-blue-600 hover:text-blue-700 font-medium text-sm">View Courses →</a>
          </div>

          <div class="group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
            <div class="text-4xl mb-4 group-hover:scale-110 transition-transform">🦴</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Biomechanics & Rehabilitation</h3>
            <p class="text-gray-600 mb-4">Motion analysis, prosthetics design, and rehabilitation robotics</p>
            <a href="biomechanics-rehabilitation-courses.html" class="text-blue-600 hover:text-blue-700 font-medium text-sm">View Courses →</a>
          </div>
        </div>

        <!-- Interactive Lectures Section -->
        <div class="mt-16 bg-gradient-to-r from-blue-50 to-purple-50 rounded-3xl p-8">
          <div class="text-center mb-8">
            <h3 class="text-3xl font-bold text-gray-900 mb-4">🎓 المحاضرات التفاعلية</h3>
            <p class="text-lg text-gray-600">تجربة تعليمية متطورة مع أدوات متحركة ومخططات تفاعلية</p>
          </div>

          <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="group bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div class="text-3xl mb-4 group-hover:scale-110 transition-transform">🧲</div>
              <h4 class="text-lg font-semibold text-gray-900 mb-2">أساسيات الرنين المغناطيسي</h4>
              <p class="text-gray-600 text-sm mb-4">محاكاة تفاعلية للبروتونات والمجالات المغناطيسية مع مخططات K-space</p>
              <div class="flex items-center justify-between mb-3">
                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">6 شرائح</span>
                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">متقدم</span>
              </div>
              <div class="flex items-center justify-between">
                <div class="flex space-x-1 space-x-reverse">
                  <span class="text-xs">⚛️ فيزياء</span>
                  <span class="text-xs">📊 مخططات</span>
                  <span class="text-xs">🎬 محاكاة</span>
                </div>
                <a href="course-mri-fundamentals.html" class="text-blue-600 hover:text-blue-700 font-medium text-sm">ابدأ التعلم →</a>
              </div>
            </div>

            <div class="group bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div class="text-3xl mb-4 group-hover:scale-110 transition-transform">💓</div>
              <h4 class="text-lg font-semibold text-gray-900 mb-2">تخطيط القلب الكهربائي</h4>
              <p class="text-gray-600 text-sm mb-4">مخططات دوائر تفاعلية وإشارات ECG متحركة مع تحليل الموجات</p>
              <div class="flex items-center justify-between mb-3">
                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">4 شرائح</span>
                <span class="text-xs bg-yellow-100 text-yellow-600 px-2 py-1 rounded-full">متوسط</span>
              </div>
              <div class="flex items-center justify-between">
                <div class="flex space-x-1 space-x-reverse">
                  <span class="text-xs">🔌 دوائر</span>
                  <span class="text-xs">📈 إشارات</span>
                  <span class="text-xs">💓 قلب</span>
                </div>
                <a href="course-ecg-fundamentals.html" class="text-green-600 hover:text-green-700 font-medium text-sm">ابدأ التعلم →</a>
              </div>
            </div>

            <div class="group bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div class="text-3xl mb-4 group-hover:scale-110 transition-transform">🏃</div>
              <h4 class="text-lg font-semibold text-gray-900 mb-2">تحليل الحركة البشرية</h4>
              <p class="text-gray-600 text-sm mb-4">محاكاة ثلاثية الأبعاد للحركة والقوى مع تحليل الميكانيكا الحيوية</p>
              <div class="flex items-center justify-between mb-3">
                <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">5 شرائح</span>
                <span class="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">مبتدئ</span>
              </div>
              <div class="flex items-center justify-between">
                <div class="flex space-x-1 space-x-reverse">
                  <span class="text-xs">🦴 عظام</span>
                  <span class="text-xs">⚡ قوى</span>
                  <span class="text-xs">📐 هندسة</span>
                </div>
                <a href="course-motion-analysis.html" class="text-purple-600 hover:text-purple-700 font-medium text-sm">ابدأ التعلم →</a>
              </div>
            </div>
          </div>

          <!-- Interactive Features Showcase -->
          <div class="mt-8 grid md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-white/50 rounded-lg">
              <div class="text-2xl mb-2">🎬</div>
              <div class="text-sm font-medium text-gray-900">رسوم متحركة</div>
              <div class="text-xs text-gray-600">CSS & SVG</div>
            </div>
            <div class="text-center p-4 bg-white/50 rounded-lg">
              <div class="text-2xl mb-2">🔧</div>
              <div class="text-sm font-medium text-gray-900">أدوات تفاعلية</div>
              <div class="text-xs text-gray-600">JavaScript</div>
            </div>
            <div class="text-center p-4 bg-white/50 rounded-lg">
              <div class="text-2xl mb-2">📊</div>
              <div class="text-sm font-medium text-gray-900">مخططات ديناميكية</div>
              <div class="text-xs text-gray-600">SVG Charts</div>
            </div>
            <div class="text-center p-4 bg-white/50 rounded-lg">
              <div class="text-2xl mb-2">🎯</div>
              <div class="text-sm font-medium text-gray-900">محاكاة واقعية</div>
              <div class="text-xs text-gray-600">Physics Engine</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- React App Section -->
    <section id="react-app" class="py-20 bg-white">
      <div class="container mx-auto px-6">
        <div class="text-center mb-12">
          <h2 class="text-4xl font-bold text-gray-900 mb-4">Interactive React Application</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience our full-featured React application with dynamic routing, state management, and interactive course selection.
          </p>
        </div>

        <div class="max-w-4xl mx-auto">
          <div id="root" class="min-h-96 bg-gray-50 rounded-2xl shadow-inner border-2 border-dashed border-gray-200 flex items-center justify-center">
            <div class="text-center">
              <div class="text-6xl mb-4">⚛️</div>
              <p class="text-gray-600">React application will load here</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-12">
    <div class="container mx-auto px-6">
      <div class="grid md:grid-cols-4 gap-8">
        <div>
          <div class="flex items-center space-x-2 mb-4">
            <div class="text-2xl">🏥</div>
            <h3 class="text-lg font-semibold">BioMed LMS</h3>
          </div>
          <p class="text-gray-400 text-sm">
            Comprehensive biomedical learning management system for medical education and professional development.
          </p>
        </div>

        <div>
          <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
          <ul class="space-y-2 text-sm">
            <li><a href="#react-app" class="text-gray-400 hover:text-white transition-colors">React Application</a></li>
            <li><a href="navigation-index.html" class="text-gray-400 hover:text-white transition-colors">Navigation Hub</a></li>
            <li><a href="enhanced-homepage-preview.html" class="text-gray-400 hover:text-white transition-colors">Enhanced Home</a></li>
            <li><a href="sitemap.html" class="text-gray-400 hover:text-white transition-colors">Site Map</a></li>
          </ul>
        </div>

        <div>
          <h4 class="text-lg font-semibold mb-4">Specializations</h4>
          <ul class="space-y-2 text-sm">
            <li><a href="subject-categories-preview.html?specialization=biomedical-imaging" class="text-gray-400 hover:text-white transition-colors">Biomedical Imaging</a></li>
            <li><a href="subject-categories-preview.html?specialization=electrophysiological" class="text-gray-400 hover:text-white transition-colors">Electrophysiology</a></li>
            <li><a href="subject-categories-preview.html?specialization=biomechanics-rehab" class="text-gray-400 hover:text-white transition-colors">Biomechanics & Rehab</a></li>
          </ul>
        </div>

        <div>
          <h4 class="text-lg font-semibold mb-4">Resources</h4>
          <ul class="space-y-2 text-sm">
            <li><a href="PROJECT_STRUCTURE.md" class="text-gray-400 hover:text-white transition-colors">Project Structure</a></li>
            <li><a href="SETUP_COMPLETE.md" class="text-gray-400 hover:text-white transition-colors">Setup Guide</a></li>
            <li><a href="assets/css/main.css" class="text-gray-400 hover:text-white transition-colors">CSS Documentation</a></li>
            <li><a href="assets/js/utils.js" class="text-gray-400 hover:text-white transition-colors">JS Utilities</a></li>
          </ul>
        </div>
      </div>

      <div class="border-t border-gray-800 mt-8 pt-8 text-center">
        <p class="text-gray-400 text-sm">&copy; 2025 BioMed LMS. All rights reserved. | Powered by AI & Modern Web Technologies</p>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script type="module" src="/src/index.tsx"></script>
  <script src="/assets/js/utils.js"></script>
  <script>
    // Enhanced page functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Hide loading screen
      setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          loadingScreen.classList.add('hidden');
        }
      }, 1500);

      // Mobile menu toggle
      const mobileMenuBtn = document.getElementById('mobile-menu-btn');
      const mobileMenu = document.getElementById('mobile-menu');

      if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', () => {
          mobileMenu.classList.toggle('hidden');
        });
      }

      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      });

      // Navigation scroll effect
      const nav = document.getElementById('main-nav');
      let lastScrollY = window.scrollY;

      window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;

        if (currentScrollY > 100) {
          nav.classList.add('bg-white/98', 'shadow-md');
        } else {
          nav.classList.remove('bg-white/98', 'shadow-md');
        }

        lastScrollY = currentScrollY;
      });

      // Add intersection observer for animations
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-fade-in');
          }
        });
      }, observerOptions);

      // Observe elements for animation
      document.querySelectorAll('.group').forEach(el => {
        observer.observe(el);
      });
    });
  </script>
</body>
</html>
