<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BioMed LMS - Enhanced Homepage</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    .gradient-bg {
      background: linear-gradient(135deg, #eff6ff 0%, #ffffff 50%, #f0fdf4 100%);
    }
    .card-hover {
      transition: all 0.3s ease;
    }
    .card-hover:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    .specialization-card {
      transition: all 0.3s ease;
    }
    .specialization-card:hover {
      transform: scale(1.05);
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }
  </style>
</head>
<body class="bg-gray-50">
  <!-- Navigation -->
  <header class="bg-blue-700 text-white shadow-md">
    <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
      <div class="flex items-center space-x-4">
        <a href="navigation-index.html" class="text-2xl font-bold hover:text-blue-200 transition-colors">🏥 BioMed LMS</a>
        <span class="text-blue-300 text-sm">/ Enhanced Homepage</span>
      </div>
      <div class="space-x-4">
        <a href="index.html" class="px-3 py-2 rounded-md text-sm font-medium bg-green-600 hover:bg-green-700 text-white">Main Landing</a>
        <a href="preview.html" class="px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-600 hover:text-white">Home</a>
        <a href="subject-categories-preview.html" class="px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-600 hover:text-white">Subject Categories</a>
        <a href="enhanced-course-details-preview.html" class="px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-600 hover:text-white">Course Details</a>
        <a href="enhanced-homepage-preview.html" class="px-3 py-2 rounded-md text-sm font-medium bg-blue-900 text-white">Enhanced Home</a>
        <a href="navigation-index.html" class="px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-600 hover:text-white">Navigation Hub</a>
      </div>
    </nav>
  </header>

  <!-- Hero Section -->
  <section class="gradient-bg py-20 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto text-center">
      <div class="mb-8">
        <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
          Welcome to
          <span class="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
            BioMed LMS
          </span>
        </h1>
        <p class="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          Your comprehensive platform for biomedical education. Discover, track, and excel in your medical learning journey.
        </p>
      </div>

      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
        <button class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
          Start Exploring
        </button>
        <button class="bg-white hover:bg-gray-50 text-blue-600 border-2 border-blue-600 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl" onclick="scrollToSpecializations()">
          Choose Your Specialization
        </button>
      </div>

      <!-- Hero Illustration -->
      <div class="relative max-w-4xl mx-auto">
        <div class="bg-gradient-to-r from-blue-100 to-green-100 rounded-2xl p-8 shadow-2xl">
          <div class="text-6xl mb-4">🏥</div>
          <p class="text-gray-600 text-lg">
            Advanced Learning Management System for Biomedical Education
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- Stats Section -->
  <section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
        <div class="text-center">
          <div class="text-4xl mb-2">🏥</div>
          <div class="text-3xl font-bold text-gray-900 mb-1">50+</div>
          <div class="text-gray-600 font-medium">LMS Platforms</div>
        </div>
        <div class="text-center">
          <div class="text-4xl mb-2">📖</div>
          <div class="text-3xl font-bold text-gray-900 mb-1">1000+</div>
          <div class="text-gray-600 font-medium">Learning Resources</div>
        </div>
        <div class="text-center">
          <div class="text-4xl mb-2">🔬</div>
          <div class="text-3xl font-bold text-gray-900 mb-1">25+</div>
          <div class="text-gray-600 font-medium">Subject Areas</div>
        </div>
        <div class="text-center">
          <div class="text-4xl mb-2">👥</div>
          <div class="text-3xl font-bold text-gray-900 mb-1">10k+</div>
          <div class="text-gray-600 font-medium">Active Learners</div>
        </div>
      </div>
    </div>
  </section>

  <!-- Specializations Preview Section -->
  <section id="specializations" class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Choose Your Specialization
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          Explore three core divisions of biomedical engineering and discover comprehensive course modules designed for your career advancement.
        </p>
      </div>

      <div class="grid md:grid-cols-3 gap-8 mb-12">
        <!-- Biomedical Imaging -->
        <div class="specialization-card group relative overflow-hidden rounded-2xl shadow-lg cursor-pointer" onclick="selectSpecialization('biomedical-imaging')">
          <div class="bg-gradient-to-br from-blue-500 to-blue-700 p-8 text-white h-full">
            <div class="text-4xl mb-4">🔬</div>
            <h3 class="text-xl font-bold mb-3 leading-tight">Biomedical Imaging Instrumentation Technology</h3>
            <p class="text-blue-100 mb-6 leading-relaxed">Master advanced imaging technologies including MRI, CT, Ultrasound, and Nuclear Medicine systems.</p>

            <div class="space-y-3 mb-6">
              <div class="flex items-center justify-between text-sm">
                <span class="font-medium">Courses Available:</span>
                <span class="bg-white bg-opacity-20 px-2 py-1 rounded">6</span>
              </div>
              <div class="flex items-center justify-between text-sm">
                <span class="font-medium">Total Credits:</span>
                <span class="bg-white bg-opacity-20 px-2 py-1 rounded">18-20 Credits</span>
              </div>
            </div>

            <div class="mb-6">
              <p class="text-sm font-medium mb-2">Featured Courses:</p>
              <ul class="text-sm space-y-1">
                <li class="flex items-center">
                  <span class="text-yellow-300 mr-2">•</span>
                  MRI Fundamentals
                </li>
                <li class="flex items-center">
                  <span class="text-yellow-300 mr-2">•</span>
                  CT Imaging Technology
                </li>
                <li class="flex items-center">
                  <span class="text-yellow-300 mr-2">•</span>
                  Medical Image Processing
                </li>
              </ul>
            </div>

            <div class="flex items-center justify-between">
              <span class="text-sm font-medium">Explore Curriculum</span>
              <svg class="w-5 h-5 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Electrophysiological -->
        <div class="specialization-card group relative overflow-hidden rounded-2xl shadow-lg cursor-pointer" onclick="selectSpecialization('electrophysiological')">
          <div class="bg-gradient-to-br from-green-500 to-green-700 p-8 text-white h-full">
            <div class="text-4xl mb-4">⚡</div>
            <h3 class="text-xl font-bold mb-3 leading-tight">Electrophysiological Instrumentation and Measurements</h3>
            <p class="text-green-100 mb-6 leading-relaxed">Specialize in electrical activity measurement including ECG, EEG, EMG, and Neural Interfaces.</p>

            <div class="space-y-3 mb-6">
              <div class="flex items-center justify-between text-sm">
                <span class="font-medium">Courses Available:</span>
                <span class="bg-white bg-opacity-20 px-2 py-1 rounded">6</span>
              </div>
              <div class="flex items-center justify-between text-sm">
                <span class="font-medium">Total Credits:</span>
                <span class="bg-white bg-opacity-20 px-2 py-1 rounded">20-22 Credits</span>
              </div>
            </div>

            <div class="mb-6">
              <p class="text-sm font-medium mb-2">Featured Courses:</p>
              <ul class="text-sm space-y-1">
                <li class="flex items-center">
                  <span class="text-yellow-300 mr-2">•</span>
                  ECG Fundamentals
                </li>
                <li class="flex items-center">
                  <span class="text-yellow-300 mr-2">•</span>
                  EEG Signal Analysis
                </li>
                <li class="flex items-center">
                  <span class="text-yellow-300 mr-2">•</span>
                  Neural Interface Technology
                </li>
              </ul>
            </div>

            <div class="flex items-center justify-between">
              <span class="text-sm font-medium">Explore Curriculum</span>
              <svg class="w-5 h-5 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Biomechanics -->
        <div class="specialization-card group relative overflow-hidden rounded-2xl shadow-lg cursor-pointer" onclick="selectSpecialization('biomechanics-rehab')">
          <div class="bg-gradient-to-br from-purple-500 to-purple-700 p-8 text-white h-full">
            <div class="text-4xl mb-4">🦴</div>
            <h3 class="text-xl font-bold mb-3 leading-tight">Biomechanics and Rehabilitation Engineering</h3>
            <p class="text-purple-100 mb-6 leading-relaxed">Focus on mechanical principles, prosthetics, orthotics, and rehabilitation robotics.</p>

            <div class="space-y-3 mb-6">
              <div class="flex items-center justify-between text-sm">
                <span class="font-medium">Courses Available:</span>
                <span class="bg-white bg-opacity-20 px-2 py-1 rounded">6</span>
              </div>
              <div class="flex items-center justify-between text-sm">
                <span class="font-medium">Total Credits:</span>
                <span class="bg-white bg-opacity-20 px-2 py-1 rounded">22-24 Credits</span>
              </div>
            </div>

            <div class="mb-6">
              <p class="text-sm font-medium mb-2">Featured Courses:</p>
              <ul class="text-sm space-y-1">
                <li class="flex items-center">
                  <span class="text-yellow-300 mr-2">•</span>
                  Gait Analysis
                </li>
                <li class="flex items-center">
                  <span class="text-yellow-300 mr-2">•</span>
                  Prosthetics Design
                </li>
                <li class="flex items-center">
                  <span class="text-yellow-300 mr-2">•</span>
                  Rehabilitation Robotics
                </li>
              </ul>
            </div>

            <div class="flex items-center justify-between">
              <span class="text-sm font-medium">Explore Curriculum</span>
              <svg class="w-5 h-5 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <div class="text-center">
        <button class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl" onclick="viewAllSpecializations()">
          View All Specializations & Course Modules
          <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  </section>

  <!-- Call to Action -->
  <section class="py-20 bg-gradient-to-r from-blue-600 to-green-600">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
      <h2 class="text-4xl font-bold text-white mb-6">
        Ready to Transform Your Biomedical Education?
      </h2>
      <p class="text-xl text-blue-100 mb-8 leading-relaxed">
        Join thousands of medical students and professionals who are advancing their careers with our comprehensive learning platform.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <button class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 shadow-lg">
          Get Started Today
        </button>
        <button class="bg-transparent text-white border-2 border-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-blue-600 transition-all duration-200 transform hover:scale-105">
          Track Progress
        </button>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-800 text-white py-6 text-center">
    <p>&copy; 2025 BioMed LMS. All rights reserved.</p>
    <p class="text-sm text-gray-400">Powered by AI</p>
  </footer>

  <script>
    function scrollToSpecializations() {
      document.getElementById('specializations').scrollIntoView({
        behavior: 'smooth'
      });
    }

    function selectSpecialization(specializationId) {
      // Simulate navigation to subject categories with specialization parameter
      alert(`Navigating to Subject Categories with specialization: ${specializationId}\n\nThis would open the detailed course modules for the selected specialization.`);

      // In a real application, this would be:
      // window.location.href = `/subject-categories?specialization=${specializationId}`;
    }

    function viewAllSpecializations() {
      // Simulate navigation to all specializations
      alert('Navigating to all Subject Categories and Course Modules');

      // In a real application, this would be:
      // window.location.href = '/subject-categories';
    }
  </script>
</body>
</html>
